import numpy as np
import torch
import re
import ast
from scipy import signal
from typing import List, Tuple, Union

def complex_to_power_spectrum(iq_data: np.ndarray, freq_bins: int,
                             sample_rate: float = None,
                             freq_min: float = 2400, freq_max: float = 2500,
                             window_type: str = 'hann') -> np.ndarray:
    """
    将复数IQ信号转换为功率谱 - 理论正确版本

    Args:
        iq_data: 复数IQ信号数组 (baseband, 中心频率为0)
        freq_bins: 目标频率bins数量
        sample_rate: 采样率 (Hz)
        freq_min: 最小频率 (MHz)
        freq_max: 最大频率 (MHz)
        window_type: 窗函数类型 ('hann', 'hamming', 'blackman', None)

    Returns:
        power_spectrum: 功率谱密度 (线性尺度, 单位: V²/Hz)
    """
    if sample_rate is None:
        from config import Config
        sample_rate = Config.SAMPLE_RATE

    N = len(iq_data)

    # 1. 应用窗函数减少频谱泄漏
    if window_type is not None:
        if window_type == 'hann':
            window = np.hanning(N)
        elif window_type == 'hamming':
            window = np.hamming(N)
        elif window_type == 'blackman':
            window = np.blackman(N)
        else:
            window = np.ones(N)

        # 窗函数功率归一化
        window_power = np.sum(window**2)
        windowed_data = iq_data * window
    else:
        windowed_data = iq_data
        window_power = N

    # 2. FFT计算 (不使用fftshift，保持原始频率顺序)
    fft_result = np.fft.fft(windowed_data, n=N)

    # 3. 计算双边功率谱密度 (理论正确)
    # PSD = |X(f)|² / (fs * N * S₁)，其中S₁是窗函数的功率归一化因子
    power_spectrum = np.abs(fft_result) ** 2 / (sample_rate * window_power)

    # 4. 转换为单边谱 (仅保留正频率部分)
    # 对于实信号，负频率部分是正频率的镜像，所以要乘以2
    # 但IQ信号是复信号，需要保留完整的双边谱

    # 5. 频率轴映射到实际频率范围
    # 原始频率轴: [0, fs/2, ..., fs-fs/N] -> [-fs/2, ..., 0, ..., fs/2]
    freqs = np.fft.fftfreq(N, 1/sample_rate)  # Hz

    # 将baseband频率映射到实际RF频率
    center_freq = (freq_min + freq_max) / 2 * 1e6  # 转换为Hz
    rf_freqs = freqs + center_freq

    # 6. 重采样到目标频率bins
    if len(power_spectrum) != freq_bins:
        from scipy.interpolate import interp1d

        # 目标频率范围
        target_freqs = np.linspace(freq_min * 1e6, freq_max * 1e6, freq_bins)

        # 确保频率范围覆盖
        valid_mask = (rf_freqs >= freq_min * 1e6) & (rf_freqs <= freq_max * 1e6)
        if np.sum(valid_mask) < 10:  # 如果有效点太少，使用全部数据
            valid_mask = np.ones_like(rf_freqs, dtype=bool)

        # 插值到目标频率网格
        f_interp = interp1d(rf_freqs[valid_mask], power_spectrum[valid_mask],
                          kind='linear', bounds_error=False,
                          fill_value=np.min(power_spectrum[valid_mask]))
        power_spectrum = f_interp(target_freqs)

    # 7. 数值稳定性处理
    power_spectrum = np.maximum(power_spectrum, 1e-15)  # 避免log(0)

    return power_spectrum

def normalize_power_spectrum(power_spectrum: np.ndarray,
                           method: str = 'robust_db',
                           db_range: float = 60.0) -> np.ndarray:
    """
    功率谱归一化 - 提供多种稳定的归一化策略

    Args:
        power_spectrum: 功率谱密度 (线性尺度)
        method: 归一化方法
            - 'robust_db': 鲁棒的dB归一化 (推荐)
            - 'minmax_db': 最小最大dB归一化
            - 'zscore': Z-score标准化
            - 'quantile': 分位数归一化
        db_range: dB动态范围

    Returns:
        normalized_spectrum: 归一化后的功率谱 [0, 1]
    """
    # 确保输入为正值
    power_spectrum = np.maximum(power_spectrum, 1e-15)

    if method == 'robust_db':
        # 转换为dB
        power_db = 10 * np.log10(power_spectrum)

        # 使用分位数确定动态范围，更鲁棒
        p95 = np.percentile(power_db, 95)
        p5 = np.percentile(power_db, 5)

        # 设置dB范围
        max_db = p95
        min_db = max_db - db_range

        # 裁剪并归一化
        power_db_clipped = np.clip(power_db, min_db, max_db)
        normalized = (power_db_clipped - min_db) / (max_db - min_db)

    elif method == 'minmax_db':
        # 传统的最小最大dB归一化
        power_db = 10 * np.log10(power_spectrum)
        min_db = np.min(power_db)
        max_db = np.max(power_db)
        normalized = (power_db - min_db) / (max_db - min_db + 1e-8)

    elif method == 'zscore':
        # Z-score标准化后映射到[0,1]
        power_db = 10 * np.log10(power_spectrum)
        mean_db = np.mean(power_db)
        std_db = np.std(power_db)
        zscore = (power_db - mean_db) / (std_db + 1e-8)
        # 映射到[0,1]，假设3-sigma范围
        normalized = np.clip((zscore + 3) / 6, 0, 1)

    elif method == 'quantile':
        # 分位数归一化，对异常值更鲁棒
        power_db = 10 * np.log10(power_spectrum)
        q01 = np.percentile(power_db, 1)
        q99 = np.percentile(power_db, 99)
        normalized = np.clip((power_db - q01) / (q99 - q01 + 1e-8), 0, 1)

    else:
        raise ValueError(f"未知的归一化方法: {method}")

    return normalized

def freq_conversion(values: np.ndarray, freq_min: float = 2400, freq_max: float = 2500,
                   n_bins: int = 1024, to_mhz: bool = True) -> np.ndarray:
    """统一的频率转换函数，支持bins<->MHz双向转换"""
    freq_resolution = (freq_max - freq_min) / n_bins
    
    if to_mhz:  # bins to MHz
        return freq_min + values * freq_resolution
    else:  # MHz to bins
        return ((values - freq_min) / freq_resolution).astype(int)

# 保持向后兼容的别名函数
def freq_bins_to_mhz(bin_indices: np.ndarray, freq_min: float = 2400, freq_max: float = 2500, n_bins: int = 1024) -> np.ndarray:
    return freq_conversion(bin_indices, freq_min, freq_max, n_bins, to_mhz=True)

def mhz_to_freq_bins(frequencies: np.ndarray, freq_min: float = 2400, freq_max: float = 2500, n_bins: int = 1024) -> np.ndarray:
    return freq_conversion(frequencies, freq_min, freq_max, n_bins, to_mhz=False)

def parse_frequency_ranges(label_str: str) -> List[Tuple[float, float]]:
    """解析频率范围字符串"""
    try:
        ranges_list = ast.literal_eval(label_str)
        return [(float(start), float(end)) for start, end in ranges_list]
    except:
        return []

def parse_question(question: str) -> Tuple[str, Tuple[float, float]]:
    """解析问题，提取任务类型和频率范围"""
    freq_pattern = r'(\d+)\s*MHz\s*-\s*(\d+)\s*MHz'
    freq_match = re.search(freq_pattern, question)
    
    freq_range = (float(freq_match.group(1)), float(freq_match.group(2))) if freq_match else (2400, 2500)
    task_type = "interference" if any(word in question.lower() for word in ["overlapping", "multiple"]) else "occupancy"
    
    return task_type, freq_range

def merge_intervals_with_gap(intervals, max_gap=2):
    """
    合并间隔小于max_gap的区间，避免过度合并
    intervals: List of (start, end)
    """
    if not intervals:
        return []
    intervals = sorted(intervals, key=lambda x: x[0])
    merged = [intervals[0]]
    for cur in intervals[1:]:
        last = merged[-1]
        if cur[0] - last[1] <= max_gap:
            merged[-1] = (last[0], max(last[1], cur[1]))
        else:
            merged.append(cur)
    return merged

def detect_segments(power_spectrum: np.ndarray, 
                   threshold: float = 0.3,
                   min_width: int = 5,
                   detect_type: str = 'occupancy') -> List[Tuple[int, int]]:
    """统一的频段检测函数，支持占用和干扰检测"""
    power_norm = (power_spectrum - power_spectrum.min()) / (power_spectrum.max() - power_spectrum.min())
    
    # 根据检测类型调整阈值
    detection_threshold = threshold + (0.2 if detect_type == 'interference' else 0.0)
    occupied = power_norm > detection_threshold
    
    segments = []
    start = None
    
    for i, is_occupied in enumerate(occupied):
        if is_occupied and start is None:
            start = i
        elif not is_occupied and start is not None:
            if i - start >= min_width:
                segments.append((start, i))
            start = None
    
    if start is not None and len(occupied) - start >= min_width:
        segments.append((start, len(occupied)))
    
    return segments

# 保持向后兼容的别名函数
def detect_occupied_segments(power_spectrum: np.ndarray, threshold: float = 0.3, min_width: int = 5) -> List[Tuple[int, int]]:
    return detect_segments(power_spectrum, threshold, min_width, 'occupancy')

def detect_interference_segments(power_spectrum: np.ndarray, all_segments: List[Tuple[int, int]], overlap_threshold: float = 0.5) -> List[Tuple[int, int]]:
    return detect_segments(power_spectrum, overlap_threshold, 5, 'interference')

def calculate_iou(pred_intervals: List[Tuple[float, float]], 
                 true_intervals: List[Tuple[float, float]], 
                 window_range: Tuple[float, float]) -> float:
    """计算IoU评分"""
    def merge_overlapping_intervals(intervals):
        if not intervals:
            return []
        sorted_intervals = sorted(intervals, key=lambda x: x[0])
        merged = [sorted_intervals[0]]
        for current in sorted_intervals[1:]:
            last = merged[-1]
            if current[0] <= last[1]:
                merged[-1] = (last[0], max(last[1], current[1]))
            else:
                merged.append(current)
        return merged
    def interval_intersection(intervals1, intervals2):
        result = []
        for start1, end1 in intervals1:
            for start2, end2 in intervals2:
                start_intersect = max(start1, start2)
                end_intersect = min(end1, end2)
                if start_intersect < end_intersect:
                    result.append((start_intersect, end_intersect))
        return merge_overlapping_intervals(result)
    def interval_union(intervals1, intervals2):
        return merge_overlapping_intervals(intervals1 + intervals2)
    window_intervals = [window_range]
    true_windowed = interval_intersection(true_intervals, window_intervals)
    intersection = interval_intersection(pred_intervals, true_windowed)
    union = interval_union(pred_intervals, true_windowed)
    
    intersection_length = sum(end - start for start, end in merge_overlapping_intervals(intersection))
    union_length = sum(end - start for start, end in merge_overlapping_intervals(union))
    
    if union_length == 0:
        return 1.0 if intersection_length == 0 else 0.0
    
    return intersection_length / union_length 