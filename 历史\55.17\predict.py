import torch
import numpy as np
from tqdm import tqdm
from typing import List, <PERSON><PERSON>

from config import Config
from dataset import load_test_data, create_test_dataloader
from model import create_model, SpectrumAnalyzer
from utils import parse_question

def predict_single_sample(analyzer: SpectrumAnalyzer, 
                         power_spectrum: torch.Tensor,
                         question: str) -> List[Tuple[float, float]]:
    """
    对单个样本进行预测
    
    Args:
        analyzer: 频谱分析器
        power_spectrum: 功率谱
        question: 问题文本
    
    Returns:
        predicted_segments: 预测的频段列表
    """
    # 解析问题
    task_type, freq_range = parse_question(question)
    
    # 进行频谱分析
    predicted_segments = analyzer.analyze_spectrum(
        power_spectrum, 
        task_type, 
        freq_range
    )
    
    return predicted_segments

def format_prediction_output(segments: List[Tuple[float, float]]) -> str:
    """
    格式化预测输出为要求的格式
    
    Args:
        segments: 频段列表 [(start, end), ...]
    
    Returns:
        formatted_str: 格式化的字符串，如 "[[2452, 2462], [2475, 2477]]"
    """
    if not segments:
        return "[]"
    
    # 将浮点数转换为整数，确保格式正确
    formatted_segments = []
    for start, end in segments:
        # 四舍五入到最近的整数
        start_int = int(round(float(start)))
        end_int = int(round(float(end)))
        
        # 确保end >= start
        if end_int < start_int:
            end_int = start_int
            
        formatted_segments.append([start_int, end_int])
    
    # 按起始频率排序
    formatted_segments.sort(key=lambda x: x[0])
    
    # 转换为严格的字符串格式，不带空格
    result = str(formatted_segments).replace(' ', '')
    
    return result

def run_prediction(model_path: str = Config.MODEL_SAVE_PATH, 
                  output_path: str = Config.OUTPUT_PATH,
                  use_pretrained: bool = True) -> None:
    """
    运行预测主函数
    
    Args:
        model_path: 模型路径
        output_path: 输出文件路径
        use_pretrained: 是否使用预训练模型
    """
    print("=" * 50)
    print("开始频谱感知预测")
    print("=" * 50)
    
    # 加载测试数据
    print("加载测试数据...")
    test_dataset = load_test_data()
    test_loader = create_test_dataloader(test_dataset, batch_size=1)
    
    # 创建分析器
    print("🚀 创建增强版频谱分析器...")
    if use_pretrained:
        try:
            analyzer = create_model(model_path)
            print(f"✅ 成功加载增强版预训练模型")
        except Exception as e:
            print(f"⚠️ 加载预训练模型失败: {e}")
            print("🔄 使用增强版架构随机初始化...")
            analyzer = create_model(None)
    else:
        print("🔄 使用增强版架构随机初始化...")
        analyzer = create_model(None)
    
    # 开始预测
    print("开始预测...")
    predictions = []
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="预测进度"):
            # 获取数据
            power_spectrum = batch['power_spectrum'].squeeze(0)  # 移除批次维度
            question = batch['question'][0]  # 获取问题字符串
            sample_idx = batch['idx'].item()
            
            # 进行预测
            predicted_segments = predict_single_sample(
                analyzer, 
                power_spectrum, 
                question
            )
            
            # 格式化输出
            formatted_prediction = format_prediction_output(predicted_segments)
            
            # 保存预测结果
            predictions.append({
                'id': sample_idx,
                'prediction': formatted_prediction,
                'question': question,
                'task_type': batch['task_type'][0],
                'freq_range': batch['freq_range']
            })
    
    # 保存预测结果到文件
    print(f"保存预测结果到: {output_path}")
    with open(output_path, 'w', encoding='utf-8') as f:
        # 写入表头 - 严格按照要求格式
        f.write("id, prediction\n")
        
        # 写入预测结果 - 确保ID按顺序输出
        sorted_predictions = sorted(predictions, key=lambda x: x['id'])
        
        for pred in sorted_predictions:
            # 严格的格式：id, prediction（注意逗号后有空格）
            f.write(f"{pred['id']}, {pred['prediction']}\n")
    
    print(f"预测完成！共处理 {len(predictions)} 个样本")
    
    # 验证输出格式
    print(f"\n📋 输出格式验证:")
    print(f"   文件路径: {output_path}")
    print(f"   样本数量: {len(predictions)}")
    
    # 检查前几行的格式
    with open(output_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        print(f"   文件行数: {len(lines)}")
        print(f"   表头: {lines[0].strip()}")
        if len(lines) > 1:
            print(f"   第一行数据: {lines[1].strip()}")
        if len(lines) > 2:
            print(f"   第二行数据: {lines[2].strip()}")
    
    # 打印一些示例结果
    print("\n📊 示例预测结果:")
    print("-" * 50)
    for i in range(min(5, len(sorted_predictions))):
        pred = sorted_predictions[i]
        print(f"样本 {pred['id']}:")
        print(f"  问题: {pred['question'][:60]}...")
        print(f"  任务类型: {pred['task_type']}")
        print(f"  预测结果: {pred['prediction']}")
        print()

def quick_test_model(model_path: str = Config.MODEL_SAVE_PATH, num_samples: int = 10):
    """
    快速测试模型性能
    
    Args:
        model_path: 模型路径
        num_samples: 测试样本数量
    """
    print("=" * 50)
    print("快速模型测试")
    print("=" * 50)
    
    # 加载测试数据
    test_dataset = load_test_data()
    
    # 创建分析器
    try:
        analyzer = create_model(model_path)
        print(f"成功加载模型: {model_path}")
    except:
        print("使用传统信号处理方法")
        analyzer = create_model(None)
    
    # 随机选择样本进行测试
    test_indices = np.random.choice(len(test_dataset), min(num_samples, len(test_dataset)), replace=False)
    
    print(f"测试 {len(test_indices)} 个随机样本...")
    
    for i, idx in enumerate(test_indices):
        sample = test_dataset[idx]
        
        power_spectrum = sample['power_spectrum']
        question = sample['question']
        task_type = sample['task_type']
        freq_range = sample['freq_range']
        
        # 进行预测
        predicted_segments = predict_single_sample(analyzer, power_spectrum, question)
        formatted_prediction = format_prediction_output(predicted_segments)
        
        print(f"\n样本 {idx} (测试 {i+1}/{len(test_indices)}):")
        print(f"  问题: {question}")
        print(f"  任务类型: {task_type}")
        print(f"  频率范围: {freq_range}")
        print(f"  预测结果: {formatted_prediction}")

def analyze_prediction_statistics(predictions_file: str = Config.OUTPUT_PATH):
    """
    分析预测结果统计信息
    
    Args:
        predictions_file: 预测结果文件路径
    """
    print("=" * 50)
    print("📊 预测结果统计分析")
    print("=" * 50)
    
    try:
        # 读取预测结果
        predictions = []
        with open(predictions_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for line_idx, line in enumerate(lines[1:], 1):  # 跳过表头
                parts = line.strip().split(', ', 1)
                if len(parts) == 2:
                    try:
                        sample_id = int(parts[0])
                        prediction_str = parts[1]
                        predictions.append((sample_id, prediction_str))
                    except ValueError:
                        print(f"⚠️  第{line_idx+1}行格式错误: {line.strip()}")
        
        print(f"✅ 总预测样本数: {len(predictions)}")
        
        # 统计空预测
        empty_predictions = sum(1 for _, pred in predictions if pred == "[]")
        print(f"📭 空预测数量: {empty_predictions} ({empty_predictions/len(predictions)*100:.1f}%)")
        
        # 统计频段数量分布
        segment_counts = []
        format_errors = 0
        
        for sample_id, pred in predictions:
            try:
                # 使用ast.literal_eval安全解析
                import ast
                segments = ast.literal_eval(pred)
                if isinstance(segments, list):
                    segment_counts.append(len(segments))
                else:
                    segment_counts.append(0)
                    format_errors += 1
            except (ValueError, SyntaxError):
                segment_counts.append(0)
                format_errors += 1
                print(f"⚠️  样本{sample_id}格式错误: {pred}")
        
        if format_errors > 0:
            print(f"❌ 格式错误数量: {format_errors}")
        
        print(f"📈 平均检测频段数: {np.mean(segment_counts):.2f}")
        print(f"📊 最大检测频段数: {max(segment_counts)}")
        print(f"📉 最小检测频段数: {min(segment_counts)}")
        
        # 频段数量分布
        from collections import Counter
        count_distribution = Counter(segment_counts)
        print(f"\n📋 频段数量分布:")
        for count, freq in sorted(count_distribution.items()):
            print(f"   {count} 个频段: {freq} 样本 ({freq/len(predictions)*100:.1f}%)")
        
        # 验证ID连续性
        expected_ids = set(range(len(predictions)))
        actual_ids = set(sample_id for sample_id, _ in predictions)
        missing_ids = expected_ids - actual_ids
        extra_ids = actual_ids - expected_ids
        
        if missing_ids:
            print(f"❌ 缺失的样本ID: {sorted(missing_ids)}")
        if extra_ids:
            print(f"❌ 多余的样本ID: {sorted(extra_ids)}")
        if not missing_ids and not extra_ids:
            print(f"✅ 样本ID完整且连续 (0 到 {len(predictions)-1})")
            
    except Exception as e:
        print(f"❌ 分析预测结果时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行预测
    run_prediction()
    
    # 分析预测结果
    analyze_prediction_statistics()
    
    print("\n预测完成！") 