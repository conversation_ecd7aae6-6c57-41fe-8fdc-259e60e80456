import numpy as np
import torch
import re
import ast
from scipy import signal
from typing import List, Tuple, Union

def complex_to_power_spectrum(iq_data: np.ndarray, freq_bins: int, 
                             sample_rate: float = None) -> np.ndarray:
    """
    将复数IQ信号转换为功率谱 - 修复版本
    
    Args:
        iq_data: 复数IQ信号数组
        freq_bins: 目标频率bins数量
        sample_rate: 采样率 (Hz)，如果为None则使用Config.SAMPLE_RATE
    
    Returns:
        归一化功率谱 (0-1范围)
    """
    if sample_rate is None:
        from config import Config
        sample_rate = Config.SAMPLE_RATE
    
    N = len(iq_data)
    
    # 1. 使用完整长度FFT获得最高频率分辨率
    fft_result = np.fft.fftshift(np.fft.fft(iq_data))
    
    # 2. 正确的功率谱密度计算
    power_spectrum = np.abs(fft_result) ** 2
    power_spectrum = power_spectrum / (sample_rate * N)  # PSD标准化，单位: V²/Hz
    
    # 3. 重采样到目标频率bins (如果需要)
    if len(power_spectrum) != freq_bins:
        from scipy.interpolate import interp1d
        # 使用归一化频率坐标
        x_orig = np.linspace(-0.5, 0.5, len(power_spectrum))
        x_target = np.linspace(-0.5, 0.5, freq_bins)
        f_interp = interp1d(x_orig, power_spectrum, kind='linear', 
                          bounds_error=False, fill_value='extrapolate')
        power_spectrum = f_interp(x_target)
    
    # 4. 转换为dB
    power_spectrum_db = 10 * np.log10(power_spectrum + 1e-12)
    
    # 5. 归一化到[0, 1]范围
    min_val = np.min(power_spectrum_db)
    max_val = np.max(power_spectrum_db)
    power_spectrum_normalized = (power_spectrum_db - min_val) / (max_val - min_val + 1e-8)
    
    return power_spectrum_normalized

def freq_conversion(values: np.ndarray, freq_min: float = 2400, freq_max: float = 2500, 
                   n_bins: int = 1024, to_mhz: bool = True) -> np.ndarray:
    """统一的频率转换函数，支持bins<->MHz双向转换"""
    freq_resolution = (freq_max - freq_min) / n_bins
    
    if to_mhz:  # bins to MHz
        return freq_min + values * freq_resolution
    else:  # MHz to bins
        return ((values - freq_min) / freq_resolution).astype(int)

# 保持向后兼容的别名函数
def freq_bins_to_mhz(bin_indices: np.ndarray, freq_min: float = 2400, freq_max: float = 2500, n_bins: int = 1024) -> np.ndarray:
    return freq_conversion(bin_indices, freq_min, freq_max, n_bins, to_mhz=True)

def mhz_to_freq_bins(frequencies: np.ndarray, freq_min: float = 2400, freq_max: float = 2500, n_bins: int = 1024) -> np.ndarray:
    return freq_conversion(frequencies, freq_min, freq_max, n_bins, to_mhz=False)

def parse_frequency_ranges(label_str: str) -> List[Tuple[float, float]]:
    """解析频率范围字符串"""
    try:
        ranges_list = ast.literal_eval(label_str)
        return [(float(start), float(end)) for start, end in ranges_list]
    except:
        return []

def parse_question(question: str) -> Tuple[str, Tuple[float, float]]:
    """解析问题，提取任务类型和频率范围"""
    freq_pattern = r'(\d+)\s*MHz\s*-\s*(\d+)\s*MHz'
    freq_match = re.search(freq_pattern, question)
    
    freq_range = (float(freq_match.group(1)), float(freq_match.group(2))) if freq_match else (2400, 2500)
    task_type = "interference" if any(word in question.lower() for word in ["overlapping", "multiple"]) else "occupancy"
    
    return task_type, freq_range

def merge_overlapping_intervals(intervals: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
    """合并重叠的区间"""
    if not intervals:
        return []
    
    sorted_intervals = sorted(intervals, key=lambda x: x[0])
    merged = [sorted_intervals[0]]
    
    for current in sorted_intervals[1:]:
        last = merged[-1]
        if current[0] <= last[1]:
            merged[-1] = (last[0], max(last[1], current[1]))
        else:
            merged.append(current)
    
    return merged

def calculate_interval_length(intervals: List[Tuple[float, float]]) -> float:
    """计算区间总长度"""
    return sum(end - start for start, end in merge_overlapping_intervals(intervals))

def interval_operations(intervals1: List[Tuple[float, float]], 
                       intervals2: List[Tuple[float, float]], 
                       operation: str = 'intersection') -> List[Tuple[float, float]]:
    """统一的区间运算函数，支持交集和并集"""
    if operation == 'intersection':
        result = []
        for start1, end1 in intervals1:
            for start2, end2 in intervals2:
                start_intersect = max(start1, start2)
                end_intersect = min(end1, end2)
                if start_intersect < end_intersect:
                    result.append((start_intersect, end_intersect))
        return merge_overlapping_intervals(result)
    
    elif operation == 'union':
        return merge_overlapping_intervals(intervals1 + intervals2)
    
    else:
        raise ValueError(f"Unsupported operation: {operation}")

# 保持向后兼容的别名函数
def interval_intersection(intervals1: List[Tuple[float, float]], intervals2: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
    return interval_operations(intervals1, intervals2, 'intersection')

def interval_union(intervals1: List[Tuple[float, float]], intervals2: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
    return interval_operations(intervals1, intervals2, 'union')

def calculate_iou(pred_intervals: List[Tuple[float, float]], 
                 true_intervals: List[Tuple[float, float]], 
                 window_range: Tuple[float, float]) -> float:
    """计算IoU评分"""
    window_intervals = [window_range]
    true_windowed = interval_intersection(true_intervals, window_intervals)
    intersection = interval_intersection(pred_intervals, true_windowed)
    union = interval_union(pred_intervals, true_windowed)
    
    intersection_length = calculate_interval_length(intersection)
    union_length = calculate_interval_length(union)
    
    if union_length == 0:
        return 1.0 if intersection_length == 0 else 0.0
    
    return intersection_length / union_length

def detect_segments(power_spectrum: np.ndarray, 
                   threshold: float = 0.3,
                   min_width: int = 5,
                   detect_type: str = 'occupancy') -> List[Tuple[int, int]]:
    """统一的频段检测函数，支持占用和干扰检测"""
    power_norm = (power_spectrum - power_spectrum.min()) / (power_spectrum.max() - power_spectrum.min())
    
    # 根据检测类型调整阈值
    detection_threshold = threshold + (0.2 if detect_type == 'interference' else 0.0)
    occupied = power_norm > detection_threshold
    
    segments = []
    start = None
    
    for i, is_occupied in enumerate(occupied):
        if is_occupied and start is None:
            start = i
        elif not is_occupied and start is not None:
            if i - start >= min_width:
                segments.append((start, i))
            start = None
    
    if start is not None and len(occupied) - start >= min_width:
        segments.append((start, len(occupied)))
    
    return segments

# 保持向后兼容的别名函数
def detect_occupied_segments(power_spectrum: np.ndarray, threshold: float = 0.3, min_width: int = 5) -> List[Tuple[int, int]]:
    return detect_segments(power_spectrum, threshold, min_width, 'occupancy')

def detect_interference_segments(power_spectrum: np.ndarray, all_segments: List[Tuple[int, int]], overlap_threshold: float = 0.5) -> List[Tuple[int, int]]:
    return detect_segments(power_spectrum, overlap_threshold, 5, 'interference') 