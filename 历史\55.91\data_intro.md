### 训练数据集介绍

本数据集为HDF5格式文件（`.h5`），文件名为`train.h5`，旨在为无线电频谱感知任务提供训练数据。数据集包含以下两个主要部分：

1. **waveforms**：
   - **描述**：包含24,000条IQ采样数据，表示在2400 MHz到2500 MHz频段内以100 MS/s采样率采集的信号。该频段内存在多个信号（2到4个）。这些信号可能频域上存在重叠，表示信号之间的干扰。
   - **数据格式**：每条数据为64位复数（complex64），包含I（实部）和Q（虚部）分量，形状为`(24000, 100000)`，其中`100000`对应1毫秒的采样点数（100 MS/s × 0.001 s）。
   - **频段范围**：覆盖2400 MHz到2500 MHz，共100 MHz带宽。

2. **labels**：
   - **描述**：包含24,000条标签，与`waveforms`一一对应，表示每条IQ采样数据中信号的占用频段范围。有多个信号（2到4个）占用频段。这些信号占用的频段可能有重叠，表示信号之间存在的干扰。
   - **数据格式**：字符串类型，内容为形如`'[[2402.0, 2422.0], [2432.0, 2472.0], [2477.0, 2479.0]]'`的`numpy.array`，表示信号占用频段的起点和终点（单位：MHz）。
   - **存储方式**：使用固定长度字符串存储（长度为最长标签的字符数），编码为UTF-8。

### 测试数据集介绍

本数据集为无标签的HDF5格式文件（`test_public.h5`）。比赛期间，public测试集对选手可见，选手可提交预测结果文件（`predictions.txt`）至系统评分，作为模型性能参考。另外还有一份选手不可见的private测试集，比赛截止后，我们将在private测试集上运行选手提交的推理代码，所得分数作为实际得分。

数据集包含以下两个主要部分：

1. **waveforms**：
   - **描述**：包含1000条IQ采样数据，表示在2400 MHz到2500 MHz频段内以100 MS/s采样率采集的信号。该频段内存在多个信号（**4到6个，比训练集的信号更多，频谱占用更拥挤**）。这些信号可能频域上存在重叠，表示信号之间的干扰。
   - **数据格式**：每条数据为64位复数（complex64），包含I（实部）和Q（虚部）分量，形状为`(1000, 100000)`，其中`100000`对应1毫秒的采样点数（100 MS/s × 0.001 s）。
   - **频段范围**：覆盖2400 MHz到2500 MHz，共100 MHz带宽。

2. **questions**：
   - **描述**：包含1000条问题，与`waveforms`一一对应，表示针对每条IQ采样数据的测试任务。
   - **数据格式**：字符串类型，编码为UTF-8，存储为固定长度字符串（长度为最长问题的字符数）。问题为英文形式，分为2类，每类300条：
     1. **频段占用范围检测**：示例问题为`"Find the occupied frequency segments within the range 2420 MHz - 2460 MHz."`，要求选手模型输出指定范围内的占用频段（如`[[2432, 2452]]`）。
     2. **干扰检测**：示例问题为`"Find the frequency segments that are occupied by multiple overlapping signals within the range 2420 MHz - 2460 MHz."`，要求选手模型识别指定范围内由多个信号重叠占用的频段（如`[[2442, 2452]]`）。

### 输出格式与评分

#### 输出格式

选手需输出名为`predictions.txt`的文本文件，包含选手模型的预测结果。文件格式如下：

- **文件结构**：每行对应一个测试样本的预测结果，包含样本的`id`和预测值。
- **格式要求**：
  - 第一行为表头：`id, prediction`。
  - 后续每行为`id, prediction`格式，其中：
    - `id`：样本编号（从0开始的整数，按顺序递增）。
    - `prediction`：一个列表，表示占用频段的区间或干扰区间，格式为`[[start1, end1], [start2, end2], ...]`，例如`[[2452, 2462], [2475, 2477]]`。
  - 字段之间使用逗号分隔（`,`）。
- **示例文件**（`predictions.txt`）：
  ```
  id, prediction
  0, [[2452, 2462], [2475, 2477]]
  1, [[2432, 2452]]
  ```

#### 评分规则

评分总分为所有样本评分的平均值。以下为具体规则：

- **评分方式**：使用IoU（Intersection over Union）计算预测区间与真实区间的重叠程度，IoU即为该样本的得分。
- **IoU计算公式**：
   $$
      \text{IoU} = \frac{|S_{\text{pred}} \cap (S_{\text{true}}\cap S_{\text{wd}})|}{|S_{\text{pred}} \cup (S_{\text{true}} \cap S_{\text{wd}})|}
   $$
   其中：
   - $S_{\text{pred}}$：预测的区间集合。
   - $S_{\text{true}}$：真实的区间集合。
   - $S_{\text{wd}}$：观测区间（问题中指定的频率范围，例如`[2412, 2442]`）。
   - $|\cdot|$：表示区间的总长度（单位：MHz）。

   - **区间重叠处理**
   在区间预测中，若预测或真实区间的列表（如`[[2432, 2452], [2442, 2462]]`）包含重叠部分，计算IoU前会合并这些区间，以去除重复计算。例如，`[[2432, 2452], [2442, 2462]]`会被合并为`[[2432, 2462]]`，长度为\( 2462 - 2432 = 30 \)，避免重叠部分`[2442, 2452]`被重复计算。
- **示例**：
   - 预测区间：`[[2432, 2452], [2462, 2472]]`
   - 真实区间：`[[2422, 2462]]`
   - 观测区间：`[2412, 2442]`（假设问题指定此范围）
   - 步骤：
      1. **计算 \( S_{\text{true}} \cap S_{\text{wd}} \)**：
         - 真实区间：`[[2422, 2462]]`
         - 观测区间：`[2412, 2442]`
         - 交集：`[[2422, 2442]]`（因为 \( 2422 \geq 2412 \)，\( 2442 \leq 2462 \)）
      2. **计算 \( S_{\text{pred}} \cap (S_{\text{true}} \cap S_{\text{wd}}) \)**：
         - 预测区间：`[[2432, 2452], [2462, 2472]]`
         - \( S_{\text{true}} \cap S_{\text{wd}} \)：`[[2422, 2442]]`
         - 交集：`[[2432, 2442]]`（因为 \( 2432 \geq 2422 \)，\( 2442 \leq 2452 \)，且`[2462, 2472]`与`[2422, 2442]`无交集）
         - 交集长度：\( 2442 - 2432 = 10 \)
      3. **计算 \( S_{\text{pred}} \cup (S_{\text{true}} \cap S_{\text{wd}}) \)**：
         - 预测区间：`[[2432, 2452], [2462, 2472]]`
         - \( S_{\text{true}} \cap S_{\text{wd}} \)：`[[2422, 2442]]`
         - 并集：`[[2422, 2452], [2462, 2472]]`（合并`[2422, 2442]`和`[2432, 2452]`，再加上`[2462, 2472]`）
         - 并集长度：\( (2452 - 2422) + (2472 - 2462) = 30 + 10 = 40 \)
      4. **计算IoU**：
         - IoU：\( \frac{10}{40} = 0.25 \)
   - 得分：0.25

- **其它情况**：
   - **长度不匹配**：
     - 如果预测文件行数少于真实答案行数，缺失的样本得0分。
     - 如果预测文件行数多于真实答案行数，多出的预测得0分。
   - **格式错误**：如果预测值无法解析（例如，语法错误或无效格式），该样本得0分。