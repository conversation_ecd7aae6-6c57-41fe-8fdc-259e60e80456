import torch
import torch.nn as nn
import h5py
import numpy as np
from torch.utils.data import Dataset, DataLoader, random_split, Subset
from typing import List, Tuple, Dict, Any
from config import Config
from utils import parse_frequency_ranges, complex_to_power_spectrum, parse_question
import random

class SpectrumAugmentation:
    """频谱数据增强类 - 专门针对RF信号的增强策略"""
    
    def __init__(self, config: Config):
        self.config = config
        self.freq_bins = config.FREQ_BINS
        self.freq_min = config.FREQ_MIN
        self.freq_max = config.FREQ_MAX
        
    def add_awgn_noise(self, iq_data: np.ndarray, snr_db_range: Tuple[float, float] = None) -> np.ndarray:
        """添加高斯白噪声"""
        if snr_db_range is None:
            snr_db_range = self.config.SNR_RANGE
        
        snr_db = np.random.uniform(*snr_db_range)
        signal_power = np.mean(np.abs(iq_data)**2)
        noise_power = signal_power / (10**(snr_db/10))
        
        noise_real = np.random.normal(0, np.sqrt(noise_power/2), iq_data.real.shape)
        noise_imag = np.random.normal(0, np.sqrt(noise_power/2), iq_data.imag.shape)
        noise = noise_real + 1j * noise_imag
        
        return iq_data + noise
    
    def frequency_shift(self, iq_data: np.ndarray, labels: List[Tuple[float, float]], 
                       max_shift_mhz: float = None) -> Tuple[np.ndarray, List[Tuple[float, float]]]:
        """频率偏移增强"""
        if max_shift_mhz is None:
            max_shift_mhz = self.config.FREQ_SHIFT_MAX
        
        shift_mhz = np.random.uniform(-max_shift_mhz, max_shift_mhz)
        
        # 检查偏移后频率是否还在有效范围内
        shifted_labels = []
        valid_shift = True
        
        for start_freq, end_freq in labels:
            new_start = start_freq + shift_mhz
            new_end = end_freq + shift_mhz
            
            # 确保偏移后的频率在有效范围内
            if new_start < self.freq_min or new_end > self.freq_max:
                valid_shift = False
                break
            
            shifted_labels.append((new_start, new_end))
        
        if not valid_shift:
            return iq_data, labels  # 如果偏移无效，返回原始数据
        
        # 应用频域偏移
        sample_rate = self.config.SAMPLE_RATE
        time_samples = len(iq_data)
        dt = 1.0 / sample_rate
        t = np.arange(time_samples) * dt
        
        # 频率偏移对应时域的复指数乘法
        shift_hz = shift_mhz * 1e6
        shifted_iq = iq_data * np.exp(1j * 2 * np.pi * shift_hz * t)
        
        return shifted_iq, shifted_labels
    
    def amplitude_scaling(self, iq_data: np.ndarray, scale_range: Tuple[float, float] = None) -> np.ndarray:
        """幅度缩放"""
        if scale_range is None:
            scale_range = self.config.AMPLITUDE_SCALE_RANGE
        
        scale_factor = np.random.uniform(*scale_range)
        return iq_data * scale_factor
    
    def phase_rotation(self, iq_data: np.ndarray, max_phase_deg: float = None) -> np.ndarray:
        """相位旋转"""
        if max_phase_deg is None:
            max_phase_deg = self.config.PHASE_ROTATION_MAX
        
        phase_rad = np.random.uniform(-max_phase_deg, max_phase_deg) * np.pi / 180
        return iq_data * np.exp(1j * phase_rad)
    
    def time_shift(self, iq_data: np.ndarray, max_shift_samples: int = None) -> np.ndarray:
        """时域偏移"""
        if max_shift_samples is None:
            max_shift_samples = self.config.TIME_SHIFT_MAX
        
        shift_samples = np.random.randint(-max_shift_samples, max_shift_samples)
        
        if shift_samples > 0:
            # 右移，前面补零
            shifted_data = np.zeros_like(iq_data)
            shifted_data[shift_samples:] = iq_data[:-shift_samples]
        elif shift_samples < 0:
            # 左移，后面补零
            shifted_data = np.zeros_like(iq_data)
            shifted_data[:shift_samples] = iq_data[-shift_samples:]
        else:
            shifted_data = iq_data.copy()
        
        return shifted_data
    
    def add_interference_signals(self, iq_data: np.ndarray, labels: List[Tuple[float, float]], 
                               num_interferers: int = None) -> Tuple[np.ndarray, List[Tuple[float, float]]]:
        """添加干扰信号以增加密集度"""
        if num_interferers is None:
            # 随机添加干扰信号
            min_count, max_count = self.config.INTERFERENCE_COUNT_RANGE
            num_interferers = np.random.randint(min_count, max_count + 1)
        
        sample_rate = self.config.SAMPLE_RATE
        time_samples = len(iq_data)
        dt = 1.0 / sample_rate
        t = np.arange(time_samples) * dt
        
        augmented_data = iq_data.copy()
        augmented_labels = labels.copy()
        
        for _ in range(num_interferers):
            # 随机选择干扰信号的频率和带宽
            bandwidth = np.random.uniform(5, 20)  # 5-20 MHz带宽
            center_freq = np.random.uniform(self.freq_min + bandwidth/2, 
                                          self.freq_max - bandwidth/2)
            
            start_freq = center_freq - bandwidth/2
            end_freq = center_freq + bandwidth/2
            
            # 检查是否与现有信号重叠太多
            overlap_too_much = False
            for existing_start, existing_end in augmented_labels:
                overlap_start = max(start_freq, existing_start)
                overlap_end = min(end_freq, existing_end)
                if overlap_start < overlap_end:
                    overlap_ratio = (overlap_end - overlap_start) / bandwidth
                    if overlap_ratio > 0.8:  # 如果重叠超过80%，跳过
                        overlap_too_much = True
                        break
            
            if overlap_too_much:
                continue
            
            # 生成干扰信号
            interferer_freq = center_freq * 1e6  # 转换为Hz
            interferer_amplitude = np.random.uniform(0.3, 0.8) * np.max(np.abs(iq_data))
            
            # 生成复调制信号
            interferer_signal = interferer_amplitude * np.exp(1j * 2 * np.pi * interferer_freq * t)
            
            # 添加随机调制
            modulation_type = np.random.choice(['none', 'am', 'fm'])
            if modulation_type == 'am':
                mod_freq = np.random.uniform(1e3, 10e3)  # 1-10 kHz调制频率
                mod_depth = np.random.uniform(0.1, 0.5)
                am_signal = 1 + mod_depth * np.sin(2 * np.pi * mod_freq * t)
                interferer_signal *= am_signal
            elif modulation_type == 'fm':
                mod_freq = np.random.uniform(1e3, 10e3)
                freq_dev = np.random.uniform(1e6, 5e6)  # 1-5 MHz频偏
                fm_phase = 2 * np.pi * freq_dev * np.cumsum(np.sin(2 * np.pi * mod_freq * t)) * dt
                interferer_signal *= np.exp(1j * fm_phase)
            
            # 添加带通滤波效果（简化版）
            interferer_signal *= np.random.uniform(0.8, 1.2, len(interferer_signal))
            
            augmented_data += interferer_signal
            augmented_labels.append((start_freq, end_freq))
        
        return augmented_data, augmented_labels
    
    def multipath_fading(self, iq_data: np.ndarray, num_paths: int = None) -> np.ndarray:
        """模拟多径衰落"""
        if num_paths is None:
            num_paths = self.config.MULTIPATH_COUNT
        
        faded_signal = iq_data.copy()
        
        for _ in range(num_paths - 1):
            # 随机延迟和衰减
            delay_samples = np.random.randint(1, 100)
            attenuation = np.random.uniform(0.1, 0.5)
            phase_shift = np.random.uniform(0, 2*np.pi)
            
            # 创建延迟信号
            delayed_signal = np.zeros_like(iq_data)
            delayed_signal[delay_samples:] = iq_data[:-delay_samples]
            delayed_signal *= attenuation * np.exp(1j * phase_shift)
            
            faded_signal += delayed_signal
        
        return faded_signal
    
    def apply_augmentation(self, iq_data: np.ndarray, labels: List[Tuple[float, float]], 
                          augmentation_prob: float = None) -> Tuple[np.ndarray, List[Tuple[float, float]]]:
        """应用随机增强组合"""
        if augmentation_prob is None:
            augmentation_prob = self.config.AUGMENTATION_PROB
        
        if np.random.random() > augmentation_prob:
            return iq_data, labels
        
        augmented_data = iq_data.copy()
        augmented_labels = labels.copy()
        
        # 随机选择增强策略
        augmentations = []
        
        # 基础增强
        if np.random.random() < self.config.NOISE_PROB:
            augmentations.append('noise')
        if np.random.random() < self.config.AMPLITUDE_PROB:
            augmentations.append('amplitude')
        if np.random.random() < self.config.PHASE_PROB:
            augmentations.append('phase')
        
        # 几何增强
        if np.random.random() < self.config.FREQ_SHIFT_PROB:
            augmentations.append('freq_shift')
        if np.random.random() < self.config.TIME_SHIFT_PROB:
            augmentations.append('time_shift')
        
        # 高级增强
        if np.random.random() < self.config.INTERFERENCE_PROB:
            augmentations.append('interference')
        if np.random.random() < self.config.MULTIPATH_PROB:
            augmentations.append('multipath')
        
        # 应用选中的增强
        for aug in augmentations:
            if aug == 'noise':
                augmented_data = self.add_awgn_noise(augmented_data)
            elif aug == 'amplitude':
                augmented_data = self.amplitude_scaling(augmented_data)
            elif aug == 'phase':
                augmented_data = self.phase_rotation(augmented_data)
            elif aug == 'freq_shift':
                augmented_data, augmented_labels = self.frequency_shift(augmented_data, augmented_labels)
            elif aug == 'time_shift':
                augmented_data = self.time_shift(augmented_data)
            elif aug == 'interference':
                augmented_data, augmented_labels = self.add_interference_signals(augmented_data, augmented_labels)
            elif aug == 'multipath':
                augmented_data = self.multipath_fading(augmented_data)
        
        return augmented_data, augmented_labels

class SpectrumDataset(Dataset):
    """
    频谱数据集类，用于训练
    处理HDF5格式的IQ数据和对应的标签
    """
    
    def __init__(self, waveforms: np.ndarray, labels: List[str], 
                 use_augmentation: bool = False, config: Config = None):
        self.waveforms = waveforms
        self.labels = labels
        self.use_augmentation = use_augmentation
        self.config = config if config is not None else Config()
        
        if use_augmentation:
            self.augmentation = SpectrumAugmentation(self.config)
        
        # 预处理标签
        self.parsed_labels = []
        for label_str in labels:
            try:
                ranges = parse_frequency_ranges(label_str)
                self.parsed_labels.append(ranges)
            except Exception as e:
                print(f"⚠️ 标签解析错误: {label_str}, 错误: {e}")
                self.parsed_labels.append([])
    
    def __len__(self):
        return len(self.waveforms)
    
    def __getitem__(self, idx):
        """
        获取单个样本
        
        Returns:
            sample: {
                'waveform': tensor,  # IQ数据 (real, imag)
                'power_spectrum': tensor,  # 功率谱
                'labels': list,  # 频段标签 [(start, end), ...]
                'idx': int  # 样本索引
            }
        """
        # 获取原始数据
        iq_data = self.waveforms[idx].astype(np.complex64)
        frequency_ranges = self.parsed_labels[idx]
        
        # 应用数据增强
        if self.use_augmentation and len(frequency_ranges) > 0:
            try:
                iq_data, frequency_ranges = self.augmentation.apply_augmentation(
                    iq_data, frequency_ranges
                )
            except Exception as e:
                print(f"⚠️ 数据增强失败: {e}")
                # 如果增强失败，使用原始数据
                pass
        
        # 转换为功率谱
        power_spectrum = self.iq_to_power_spectrum(iq_data)
        
        return {
            'waveform': torch.from_numpy(iq_data.real.astype(np.float32)).float(),
            'power_spectrum': torch.from_numpy(power_spectrum).float(),
            'labels': frequency_ranges,
            'idx': idx,
            'raw_iq': iq_data  # 保留原始IQ数据用于调试
        }
    
    def iq_to_power_spectrum(self, iq_data: np.ndarray) -> np.ndarray:
        """将IQ数据转换为功率谱 - 修复版本，使用理论正确的方法"""
        
        N = len(iq_data)
        fs = self.config.SAMPLE_RATE
        target_bins = self.config.FREQ_BINS
        
        # 1. 使用完整长度FFT保持最高频率分辨率
        fft_result = np.fft.fftshift(np.fft.fft(iq_data))
        
        # 2. 正确的功率谱密度计算
        power_spectrum = np.abs(fft_result) ** 2
        power_spectrum = power_spectrum / (fs * N)  # PSD标准化，单位: V²/Hz
        
        # 3. 重采样到目标bins数量 (如果需要)
        if len(power_spectrum) != target_bins:
            from scipy.interpolate import interp1d
            # 使用归一化频率坐标进行插值
            x_orig = np.linspace(-0.5, 0.5, len(power_spectrum))
            x_target = np.linspace(-0.5, 0.5, target_bins)
            f_interp = interp1d(x_orig, power_spectrum, kind='linear', 
                              bounds_error=False, fill_value='extrapolate')
            power_spectrum = f_interp(x_target)
        
        # 4. 转换为dB
        power_spectrum_db = 10 * np.log10(power_spectrum + 1e-12)
        
        # 5. 归一化到[0, 1]范围
        min_val = np.min(power_spectrum_db)
        max_val = np.max(power_spectrum_db)
        power_spectrum_normalized = (power_spectrum_db - min_val) / (max_val - min_val + 1e-8)
        
        return power_spectrum_normalized

class TestDataset(Dataset):
    """
    测试数据集类，处理包含问题的测试数据
    """
    
    def __init__(self, waveforms: np.ndarray, questions: List[str], 
                 use_augmentation: bool = False, config: Config = None):
        self.waveforms = waveforms
        self.questions = questions
        self.use_augmentation = use_augmentation
        self.config = config if config is not None else Config()
        
        if use_augmentation:
            self.augmentation = SpectrumAugmentation(self.config)
        
        # 预处理问题
        self.parsed_questions = []
        for question in questions:
            try:
                task_type, freq_range = parse_question(question)
                self.parsed_questions.append((task_type, freq_range))
            except Exception as e:
                print(f"⚠️ 问题解析错误: {question}, 错误: {e}")
                self.parsed_questions.append(('[]', (0, 0)))
    
    def __len__(self):
        return len(self.waveforms)
    
    def __getitem__(self, idx):
        """
        获取单个测试样本
        
        Returns:
            sample: {
                'waveform': tensor,  # IQ数据 (real, imag)
                'power_spectrum': tensor,  # 功率谱
                'question': str,  # 问题文本
                'task_type': str,  # 任务类型
                'freq_range': tuple,  # 频率范围
                'idx': int  # 样本索引
            }
        """
        # 获取原始数据
        iq_data = self.waveforms[idx].astype(np.complex64)
        task_type, freq_range = self.parsed_questions[idx]
        
        # 应用数据增强
        if self.use_augmentation:
            try:
                iq_data, _ = self.augmentation.apply_augmentation(
                    iq_data, [(freq_range[0], freq_range[1])]
                )
            except Exception as e:
                print(f"⚠️ 数据增强失败: {e}")
                # 如果增强失败，使用原始数据
                pass
        
        # 转换为功率谱
        power_spectrum = self.iq_to_power_spectrum(iq_data)
        
        return {
            'waveform': torch.from_numpy(iq_data.real.astype(np.float32)).float(),
            'power_spectrum': torch.from_numpy(power_spectrum).float(),
            'question': self.questions[idx],
            'task_type': task_type,
            'freq_range': freq_range,
            'idx': idx
        }
    
    def iq_to_power_spectrum(self, iq_data: np.ndarray) -> np.ndarray:
        """将IQ数据转换为功率谱 - 修复版本，使用理论正确的方法"""
        
        N = len(iq_data)
        fs = self.config.SAMPLE_RATE
        target_bins = self.config.FREQ_BINS
        
        # 1. 使用完整长度FFT保持最高频率分辨率
        fft_result = np.fft.fftshift(np.fft.fft(iq_data))
        
        # 2. 正确的功率谱密度计算
        power_spectrum = np.abs(fft_result) ** 2
        power_spectrum = power_spectrum / (fs * N)  # PSD标准化，单位: V²/Hz
        
        # 3. 重采样到目标bins数量 (如果需要)
        if len(power_spectrum) != target_bins:
            from scipy.interpolate import interp1d
            # 使用归一化频率坐标进行插值
            x_orig = np.linspace(-0.5, 0.5, len(power_spectrum))
            x_target = np.linspace(-0.5, 0.5, target_bins)
            f_interp = interp1d(x_orig, power_spectrum, kind='linear', 
                              bounds_error=False, fill_value='extrapolate')
            power_spectrum = f_interp(x_target)
        
        # 4. 转换为dB
        power_spectrum_db = 10 * np.log10(power_spectrum + 1e-12)
        
        # 5. 归一化到[0, 1]范围
        min_val = np.min(power_spectrum_db)
        max_val = np.max(power_spectrum_db)
        power_spectrum_normalized = (power_spectrum_db - min_val) / (max_val - min_val + 1e-8)
        
        return power_spectrum_normalized

def create_train_dataloaders(dataset: SpectrumDataset, 
                           train_split: float = Config.TRAIN_SPLIT,
                           batch_size: int = Config.BATCH_SIZE) -> Tuple[DataLoader, DataLoader]:
    """
    创建训练和验证数据加载器
    
    Args:
        dataset: 数据集
        train_split: 训练集比例
        batch_size: 批大小
    
    Returns:
        train_loader, val_loader: 训练和验证数据加载器
    """
    # 计算训练集和验证集大小
    total_size = len(dataset)
    train_size = int(train_split * total_size)
    val_size = total_size - train_size
    
    print(f"训练集大小: {train_size}, 验证集大小: {val_size}")
    
    # 随机分割数据集
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])
    
    # 创建一个不使用增强的数据集用于验证
    val_dataset_no_aug = SpectrumDataset(
        dataset.waveforms, 
        dataset.labels,
        use_augmentation=False,  # 验证集不使用增强
        config=Config
    )
    
    # 使用相同的索引分割验证数据集
    val_indices = val_dataset.indices
    val_dataset_no_aug = Subset(val_dataset_no_aug, val_indices)
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=True,
        num_workers=0,  # Windows兼容性
        pin_memory=True if torch.cuda.is_available() else False,
        collate_fn=custom_collate_fn
    )
    
    val_loader = DataLoader(
        val_dataset_no_aug,
        batch_size=batch_size,
        shuffle=False,
        num_workers=0,
        pin_memory=True if torch.cuda.is_available() else False,
        collate_fn=custom_collate_fn
    )
    
    return train_loader, val_loader

def create_test_dataloader(test_dataset: TestDataset, 
                          batch_size: int = 1) -> DataLoader:
    """
    创建测试数据加载器
    
    Args:
        test_dataset: 测试数据集
        batch_size: 批大小（测试时通常为1）
    
    Returns:
        test_loader: 测试数据加载器
    """
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=0,  # Windows上设为0避免多进程问题
        pin_memory=True,
        collate_fn=custom_test_collate_fn
    )
    
    return test_loader

# 辅助函数
def load_train_data(data_path: str = Config.TRAIN_DATA_PATH) -> SpectrumDataset:
    """
    加载训练数据
    
    Args:
        data_path: 数据文件路径
    
    Returns:
        dataset: 数据集对象
    """
    print(f"数据集加载: {data_path}")
    
    with h5py.File(data_path, 'r') as f:
        waveforms = f['waveforms'][:]
        labels = f['labels'][:]
        
        # 将字节字符串转换为普通字符串
        if isinstance(labels[0], bytes):
            labels = [label.decode('utf-8') for label in labels]
    
    print(f"波形数据形状: {waveforms.shape}")
    print(f"标签数据数量: {len(labels)}")
    
    # 使用数据增强创建训练数据集
    dataset = SpectrumDataset(waveforms, labels, use_augmentation=True, config=Config())
    
    return dataset

def load_test_data(data_path: str = Config.TEST_DATA_PATH) -> TestDataset:
    """
    加载测试数据
    
    Args:
        data_path: 测试数据文件路径
    
    Returns:
        dataset: 测试数据集对象
    """
    print(f"测试数据集加载: {data_path}")
    
    with h5py.File(data_path, 'r') as f:
        waveforms = f['waveforms'][:]
        questions = f['questions'][:]
        
        # 将字节字符串转换为普通字符串
        if isinstance(questions[0], bytes):
            questions = [q.decode('utf-8') for q in questions]
    
    print(f"测试波形数据形状: {waveforms.shape}")
    print(f"测试问题数量: {len(questions)}")
    
    # 测试数据集不使用数据增强
    dataset = TestDataset(waveforms, questions, use_augmentation=False, config=Config())
    
    return dataset

def custom_collate_fn(batch):
    """
    自定义批次整理函数，确保所有样本形状一致
    
    Args:
        batch: 样本列表
    
    Returns:
        collated_batch: 整理后的批次数据
    """
    # 分离不同类型的数据
    waveforms = []
    power_spectrums = []
    labels = []
    indices = []
    
    for sample in batch:
        waveforms.append(sample['waveform'])
        power_spectrums.append(sample['power_spectrum'])
        labels.append(sample['labels'])
        indices.append(sample['idx'])
    
    # 堆叠张量数据
    waveforms_batch = torch.stack(waveforms, dim=0)  # (batch_size, 2, 100000)
    power_spectrums_batch = torch.stack(power_spectrums, dim=0)  # (batch_size, freq_bins)
    indices_batch = torch.tensor(indices)
    
    return {
        'waveform': waveforms_batch,
        'power_spectrum': power_spectrums_batch,
        'labels': labels,  # 保持为列表
        'idx': indices_batch
    }

def custom_test_collate_fn(batch):
    """
    测试数据的自定义批次整理函数
    """
    # 分离不同类型的数据
    waveforms = []
    power_spectrums = []
    questions = []
    task_types = []
    freq_ranges = []
    indices = []
    
    for sample in batch:
        waveforms.append(sample['waveform'])
        power_spectrums.append(sample['power_spectrum'])
        questions.append(sample['question'])
        task_types.append(sample['task_type'])
        freq_ranges.append(sample['freq_range'])
        indices.append(sample['idx'])
    
    # 堆叠张量数据
    waveforms_batch = torch.stack(waveforms, dim=0)
    power_spectrums_batch = torch.stack(power_spectrums, dim=0)
    indices_batch = torch.tensor(indices)
    
    return {
        'waveform': waveforms_batch,
        'power_spectrum': power_spectrums_batch,
        'question': questions,
        'task_type': task_types,
        'freq_range': freq_ranges,
        'idx': indices_batch
    }

# 数据变换函数
# class SpectrumNormalize(nn.Module):
#     \"\"\"功率谱归一化变换\"\"\"
    
#     def __init__(self):
#         super(SpectrumNormalize, self).__init__()
    
#     def forward(self, sample):
#         power_spectrum = sample[\'power_spectrum\']
#         # Z-score标准化
#         mean = power_spectrum.mean()
#         std = power_spectrum.std()
#         if std > 0:
#             power_spectrum = (power_spectrum - mean) / std
#         sample[\'power_spectrum\'] = power_spectrum
#         return sample

# class AddNoise(nn.Module):
#     \"\"\"添加噪声增强\"\"\"
    
#     def __init__(self, noise_level=0.01):\
#         super(AddNoise, self).__init__()
#         self.noise_level = noise_level
    
#     def forward(self, sample):
#         if torch.rand(1) > 0.5:  # 50%概率添加噪声
#             noise = torch.randn_like(sample[\'power_spectrum\']) * self.noise_level
#             sample[\'power_spectrum\'] += noise
#         return sample 