import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import List, <PERSON><PERSON>
from config import Config
from utils import (detect_occupied_segments, detect_interference_segments, 
                  freq_bins_to_mhz, mhz_to_freq_bins, merge_intervals_with_gap)

class PositionalEncoding(nn.Module):
    """位置编码模块，为Transformer提供位置信息"""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        return x + self.pe[:x.size(0), :]

class EfficientSpectrumCNN(nn.Module):
    """高效频谱CNN - 重新设计，专注于频谱检测任务"""

    def __init__(self, input_size: int = Config.FREQ_BINS, embed_dim: int = 512):
        super(EfficientSpectrumCNN, self).__init__()

        self.input_size = input_size
        self.embed_dim = embed_dim

        # 计算理论感受野需求
        # 20MHz信号 = 20/100*4096 ≈ 819 bins (对于4096 bins, 100MHz带宽)
        # 目标感受野: 1000+ bins

        # 使用更高效的架构：深度可分离卷积 + 残差连接
        self.stem = nn.Sequential(
            nn.Conv1d(1, 64, kernel_size=7, padding=3, bias=False),
            nn.BatchNorm1d(64),
            nn.GELU(),
        )

        # 多尺度特征提取块 - 使用深度可分离卷积提高效率
        self.feature_blocks = nn.ModuleList([
            self._make_efficient_block(64, 128, kernel_size=15, dilation=1),   # RF: ~15
            self._make_efficient_block(128, 256, kernel_size=21, dilation=2),  # RF: ~57
            self._make_efficient_block(256, 384, kernel_size=31, dilation=4),  # RF: ~181
            self._make_efficient_block(384, embed_dim, kernel_size=41, dilation=8),  # RF: ~509
        ])

        # 全局上下文模块 - 替代复杂的Transformer
        self.global_context = nn.Sequential(
            nn.AdaptiveAvgPool1d(1),
            nn.Conv1d(embed_dim, embed_dim // 4, 1),
            nn.GELU(),
            nn.Conv1d(embed_dim // 4, embed_dim, 1),
            nn.Sigmoid()
        )

        # 特征融合 - 多尺度特征融合
        self.feature_fusion = nn.Sequential(
            nn.Conv1d(embed_dim, embed_dim, kernel_size=1, bias=False),
            nn.BatchNorm1d(embed_dim),
            nn.GELU(),
            nn.Dropout(0.1)
        )

    def _make_efficient_block(self, in_channels, out_channels, kernel_size, dilation):
        """创建高效的特征提取块"""
        # 计算正确的padding
        padding = ((kernel_size - 1) * dilation) // 2

        return nn.Sequential(
            # 标准卷积 (先简化，避免深度可分离卷积的复杂性)
            nn.Conv1d(in_channels, out_channels, kernel_size=kernel_size,
                     padding=padding, dilation=dilation, bias=False),
            nn.BatchNorm1d(out_channels),
            nn.GELU(),

            # 点卷积进行特征细化
            nn.Conv1d(out_channels, out_channels, kernel_size=1, bias=False),
            nn.BatchNorm1d(out_channels),
            nn.GELU(),
        )

    def forward(self, x):
        """前向传播"""
        if len(x.shape) == 2:
            x = x.unsqueeze(1)  # (batch, 1, freq_bins)

        # Stem处理
        x = self.stem(x)  # (batch, 64, freq_bins)

        # 多尺度特征提取
        features = []
        for i, block in enumerate(self.feature_blocks):
            residual = x
            x = block(x)

            # 简化的残差连接 - 只在通道数匹配时使用
            if residual.shape[1] == x.shape[1]:
                x = x + residual

            features.append(x)

        # 全局上下文增强
        context = self.global_context(x)  # (batch, embed_dim, 1)
        x = x * context  # 广播乘法

        # 特征融合
        x = self.feature_fusion(x)  # (batch, embed_dim, freq_bins)

        return x

class EfficientSpectrumDetector(nn.Module):
    """高效频谱检测器 - 简化架构，专注核心任务"""

    def __init__(self, input_size: int = Config.FREQ_BINS):
        super(EfficientSpectrumDetector, self).__init__()

        self.input_size = input_size
        embed_dim = Config.EMBED_DIM

        # 主干特征提取网络
        self.backbone = EfficientSpectrumCNN(input_size, embed_dim)

        # 多尺度检测头 - 处理不同尺度的信号
        self.detection_heads = nn.ModuleList([
            # 细粒度检测头 (小信号)
            nn.Sequential(
                nn.Conv1d(embed_dim, 256, kernel_size=3, padding=1),
                nn.BatchNorm1d(256),
                nn.GELU(),
                nn.Conv1d(256, 128, kernel_size=3, padding=1),
                nn.BatchNorm1d(128),
                nn.GELU(),
                nn.Conv1d(128, 1, kernel_size=1),
            ),
            # 中等尺度检测头
            nn.Sequential(
                nn.Conv1d(embed_dim, 256, kernel_size=5, padding=2),
                nn.BatchNorm1d(256),
                nn.GELU(),
                nn.Conv1d(256, 128, kernel_size=5, padding=2),
                nn.BatchNorm1d(128),
                nn.GELU(),
                nn.Conv1d(128, 1, kernel_size=1),
            ),
            # 粗粒度检测头 (大信号)
            nn.Sequential(
                nn.Conv1d(embed_dim, 256, kernel_size=7, padding=3),
                nn.BatchNorm1d(256),
                nn.GELU(),
                nn.Conv1d(256, 128, kernel_size=7, padding=3),
                nn.BatchNorm1d(128),
                nn.GELU(),
                nn.Conv1d(128, 1, kernel_size=1),
            )
        ])

        # 特征融合层
        self.fusion = nn.Sequential(
            nn.Conv1d(len(self.detection_heads), 64, kernel_size=1),
            nn.BatchNorm1d(64),
            nn.GELU(),
            nn.Conv1d(64, 1, kernel_size=1)
        )

        # 置信度估计分支
        self.confidence_head = nn.Sequential(
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(embed_dim, 256),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )

    def forward(self, x, task_type="occupancy"):
        """前向传播 - 统一的检测接口"""
        if len(x.shape) == 2:
            x = x.unsqueeze(1)  # (batch, 1, freq_bins)

        # 特征提取
        features = self.backbone(x)  # (batch, embed_dim, freq_bins)

        # 多尺度检测
        detections = []
        for head in self.detection_heads:
            detection = head(features)  # (batch, 1, freq_bins)
            detections.append(detection)

        # 堆叠多尺度结果
        multi_scale = torch.cat(detections, dim=1)  # (batch, num_heads, freq_bins)

        # 融合多尺度检测结果
        final_detection = self.fusion(multi_scale)  # (batch, 1, freq_bins)
        final_detection = final_detection.squeeze(1)  # (batch, freq_bins)

        # 置信度估计
        confidence = self.confidence_head(features)  # (batch, 1)

        return final_detection, confidence, None  # 保持接口兼容性

class SpectrumDetector(nn.Module):
    """频谱检测器 - 重新设计的高效架构"""

    def __init__(self):
        super(SpectrumDetector, self).__init__()

        # 使用新的高效检测器
        self.detector = EfficientSpectrumDetector(Config.FREQ_BINS)

        # 任务适配层 - 根据不同任务调整输出
        self.task_adapters = nn.ModuleDict({
            'occupancy': nn.Identity(),  # 占用检测直接使用原始输出
            'interference': nn.Sequential(  # 干扰检测需要更高的敏感度
                nn.Conv1d(1, 32, kernel_size=3, padding=1),
                nn.GELU(),
                nn.Conv1d(32, 1, kernel_size=1),
            ),
            'overlap': nn.Sequential(  # 重叠检测输出4个类别
                nn.Conv1d(1, 64, kernel_size=5, padding=2),
                nn.GELU(),
                nn.Conv1d(64, 4, kernel_size=1),
            )
        })

    def forward(self, power_spectrum, task_type="occupancy"):
        """统一的前向传播接口"""
        # 基础检测
        base_detection, confidence, _ = self.detector(power_spectrum, task_type)

        # 任务特定适配
        if task_type in self.task_adapters:
            if task_type == "overlap":
                # 重叠检测返回4类输出
                adapted_output = self.task_adapters[task_type](base_detection.unsqueeze(1))
                return adapted_output.transpose(1, 2), confidence, None  # (batch, freq_bins, 4)
            else:
                # 其他任务返回单通道输出
                if task_type == "interference":
                    adapted_output = self.task_adapters[task_type](base_detection.unsqueeze(1))
                    return adapted_output.squeeze(1), confidence, None
                else:
                    return base_detection, confidence, None
        else:
            return base_detection, confidence, None

class SpectrumAnalyzer:
    """频谱分析器 - 使用CNN+Transformer进行分析"""
    
    def __init__(self, model: SpectrumDetector = None, device: str = Config.DEVICE):
        self.device = device
        
        if model is None:
            self.model = SpectrumDetector().to(device)
        else:
            self.model = model.to(device)
            
        self.model.eval()
        
        # 高斯平滑核
        self.smooth_kernel = self._create_gaussian_kernel(Config.SMOOTHING_WINDOW)
        
        # 自适应阈值参数
        self.base_threshold_occ = 0.3       # 占用检测基础阈值
        self.base_threshold_int = 0.25      # 干扰检测基础阈值
        self.density_factor = 0.05          # 密度调整因子
        self.confidence_factor = 0.1        # 置信度调整因子
        self.freq_range_factor = 0.02       # 频率范围调整因子
    
    def _create_gaussian_kernel(self, window_size: int) -> torch.Tensor:
        """创建高斯平滑核"""
        sigma = window_size / 3.0
        x = torch.arange(window_size, dtype=torch.float32) - window_size // 2
        kernel = torch.exp(-0.5 * (x / sigma) ** 2)
        return kernel / kernel.sum()
    
    def _smooth_detection(self, detection: np.ndarray) -> np.ndarray:
        """平滑检测结果"""
        if len(detection) < len(self.smooth_kernel):
            return detection
            
        # 使用卷积进行平滑
        kernel = self.smooth_kernel.numpy()
        padded = np.pad(detection, len(kernel)//2, mode='edge')
        smoothed = np.convolve(padded, kernel, mode='valid')
        return smoothed
    
    def _estimate_signal_density(self, detection_prob: np.ndarray) -> float:
        """估计信号密度"""
        # 使用高于某个阈值的点的比例来估计密度
        signal_ratio = np.mean(detection_prob > 0.2)
        return signal_ratio
    
    def _compute_adaptive_threshold(self, detection_prob: np.ndarray, 
                                   confidence: np.ndarray, 
                                   freq_range: Tuple[float, float],
                                   task_type: str = "occupancy") -> float:
        """
        计算自适应阈值
        
        Args:
            detection_prob: 检测概率
            confidence: 置信度
            freq_range: 频率范围
            task_type: 任务类型
        
        Returns:
            adaptive_threshold: 自适应阈值
        """
        # 选择基础阈值
        base_threshold = self.base_threshold_occ if task_type == "occupancy" else self.base_threshold_int
        
        # 估计信号密度
        signal_density = self._estimate_signal_density(detection_prob)
        
        # 计算平均置信度
        avg_confidence = np.mean(confidence)
        
        # 计算频率范围因子 (范围越小，阈值越低，更敏感)
        freq_span = freq_range[1] - freq_range[0]
        freq_factor = max(0, (100 - freq_span) / 100) * self.freq_range_factor
        
        # 自适应调整
        # 1. 高信号密度 -> 降低阈值 (更容易检测到信号)
        # 2. 高置信度 -> 降低阈值 (模型更确定)
        # 3. 小频率范围 -> 降低阈值 (更精细检测)
        adaptive_threshold = (base_threshold - 
                            self.density_factor * signal_density - 
                            self.confidence_factor * (avg_confidence - 0.5) -
                            freq_factor)
        
        # 限制阈值范围
        if task_type == "occupancy":
            adaptive_threshold = np.clip(adaptive_threshold, 0.15, 0.55)
        else:  # interference
            adaptive_threshold = np.clip(adaptive_threshold, 0.1, 0.45)
        
        return adaptive_threshold
    
    def _apply_nms(self, segments: List[Tuple[float, float]], 
                   scores: List[float], threshold: float = 0.3) -> List[Tuple[float, float]]:
        """非最大抑制去除重叠检测"""
        if not segments:
            return segments
            
        # 按分数排序
        sorted_indices = sorted(range(len(scores)), key=lambda i: scores[i], reverse=True)
        
        keep = []
        for i in sorted_indices:
            seg_i = segments[i]
            keep_current = True
            
            for j in keep:
                seg_j = segments[j]
                # 计算重叠度
                overlap_start = max(seg_i[0], seg_j[0])
                overlap_end = min(seg_i[1], seg_j[1])
                
                if overlap_start < overlap_end:
                    overlap = overlap_end - overlap_start
                    union = (seg_i[1] - seg_i[0]) + (seg_j[1] - seg_j[0]) - overlap
                    iou = overlap / union if union > 0 else 0
                    
                    if iou > threshold:
                        keep_current = False
                        break
            
            if keep_current:
                keep.append(i)
        
        return [segments[i] for i in keep]
    
    def detect_occupancy(self, power_spectrum: torch.Tensor, 
                        freq_range: Tuple[float, float],
                        use_ml: bool = True) -> List[Tuple[float, float]]:
        """占用检测使用CNN+Transformer，支持多阈值集成"""
        all_segments = []
        thresholds = [0.25, 0.3, 0.35, 0.4]  # 多阈值集成
        if use_ml and hasattr(self.model, 'eval'):
            with torch.no_grad():
                if len(power_spectrum.shape) == 1:
                    power_spectrum = power_spectrum.unsqueeze(0)
                power_spectrum = power_spectrum.to(self.device)
                detection_logits, confidence = self.model(power_spectrum, "occupancy")
                detection_prob = torch.sigmoid(detection_logits).cpu().numpy().squeeze()
                detection_smooth = self._smooth_detection(detection_prob)
                # 多阈值集成
                segments_bins_all = []
                for th in thresholds:
                    segments_bins = detect_occupied_segments(detection_smooth, threshold=th)
                    segments_bins_all.extend(segments_bins)
                # 区间合并优化
                segments_bins_all = merge_intervals_with_gap(segments_bins_all, max_gap=2)
                for start_bin, end_bin in segments_bins_all:
                    start_freq = freq_bins_to_mhz(np.array([start_bin]), 
                                                Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
                    end_freq = freq_bins_to_mhz(np.array([end_bin]), 
                                              Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
                    all_segments.append((start_freq, end_freq))
        # 应用非最大抑制
        if all_segments:
            all_segments = self._apply_nms(all_segments, [1.0] * len(all_segments), 0.25)
        # 过滤到指定频率范围
        filtered_segments = []
        for start_freq, end_freq in all_segments:
            if start_freq >= freq_range[0] and end_freq <= freq_range[1]:
                if end_freq - start_freq >= Config.MIN_SEGMENT_WIDTH:
                    filtered_segments.append((start_freq, end_freq))
            elif start_freq < freq_range[1] and end_freq > freq_range[0]:
                clipped_start = max(start_freq, freq_range[0])
                clipped_end = min(end_freq, freq_range[1])
                if clipped_end - clipped_start >= Config.MIN_SEGMENT_WIDTH:
                    filtered_segments.append((clipped_start, clipped_end))
        return filtered_segments
    
    def detect_interference(self, power_spectrum: torch.Tensor,
                          freq_range: Tuple[float, float],
                          use_ml: bool = True) -> List[Tuple[float, float]]:
        """干扰检测使用CNN+Transformer，支持多阈值集成"""
        all_segments = []
        thresholds = [0.2, 0.25, 0.3, 0.35]  # 干扰检测可用更低阈值
        if use_ml and hasattr(self.model, 'eval'):
            with torch.no_grad():
                if len(power_spectrum.shape) == 1:
                    power_spectrum = power_spectrum.unsqueeze(0)
                power_spectrum = power_spectrum.to(self.device)
                detection_logits, confidence = self.model(power_spectrum, "interference")
                detection_prob = torch.sigmoid(detection_logits).cpu().numpy().squeeze()
                detection_smooth = self._smooth_detection(detection_prob)
                segments_bins_all = []
                for th in thresholds:
                    segments_bins = detect_occupied_segments(detection_smooth, threshold=th)
                    segments_bins_all.extend(segments_bins)
                segments_bins_all = merge_intervals_with_gap(segments_bins_all, max_gap=2)
                for start_bin, end_bin in segments_bins_all:
                    start_freq = freq_bins_to_mhz(np.array([start_bin]), 
                                                Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
                    end_freq = freq_bins_to_mhz(np.array([end_bin]), 
                                              Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
                    if start_freq >= freq_range[0] and end_freq <= freq_range[1]:
                        if end_freq - start_freq >= Config.MIN_SEGMENT_WIDTH:
                            all_segments.append((start_freq, end_freq))
                    elif start_freq < freq_range[1] and end_freq > freq_range[0]:
                        clipped_start = max(start_freq, freq_range[0])
                        clipped_end = min(end_freq, freq_range[1])
                        if clipped_end - clipped_start >= Config.MIN_SEGMENT_WIDTH:
                            all_segments.append((clipped_start, clipped_end))
            if all_segments:
                all_segments = self._apply_nms(all_segments, [1.0] * len(all_segments), 0.25)
            return all_segments
        # 传统方法兜底
        all_segments = self.detect_occupancy(power_spectrum, (Config.FREQ_MIN, Config.FREQ_MAX), False)
        interference_segments = []
        for i, seg1 in enumerate(all_segments):
            for j, seg2 in enumerate(all_segments[i+1:], i+1):
                overlap_start = max(seg1[0], seg2[0])
                overlap_end = min(seg1[1], seg2[1])
                if overlap_start < overlap_end:
                    if overlap_start >= freq_range[0] and overlap_end <= freq_range[1]:
                        if overlap_end - overlap_start >= Config.MIN_SEGMENT_WIDTH:
                            interference_segments.append((overlap_start, overlap_end))
        return interference_segments
    
    def analyze_spectrum(self, power_spectrum: torch.Tensor, 
                        task_type: str, 
                        freq_range: Tuple[float, float]) -> List[Tuple[float, float]]:
        """根据任务类型分析频谱"""
        if task_type == "occupancy":
            return self.detect_occupancy(power_spectrum, freq_range)
        elif task_type == "interference":
            return self.detect_interference(power_spectrum, freq_range)
        else:
            return self.detect_occupancy(power_spectrum, freq_range)

def create_model(pretrained_path: str = None) -> SpectrumAnalyzer:
    """创建分析模型"""
    detector = SpectrumDetector()
    
    if pretrained_path and torch.cuda.is_available():
        try:
            checkpoint = torch.load(pretrained_path)
            detector.load_state_dict(checkpoint['model_state_dict'])
            print(f"✅ 成功加载预训练模型: {pretrained_path}")
        except Exception as e:
            print(f"⚠️ 无法加载预训练模型: {e}，使用随机初始化")
    
    analyzer = SpectrumAnalyzer(detector)
    return analyzer 