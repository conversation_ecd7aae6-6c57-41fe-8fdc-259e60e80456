import torch
import torch.nn as nn
import torch.optim as optim
from torch.amp import autocast, GradScaler
import numpy as np
import time
from tqdm import tqdm
from typing import Dict, List, Tuple
import os
import torch.nn.functional as F

from config import Config
from dataset import load_train_data, create_train_dataloaders
from model import SpectrumDetector, create_model
from utils import mhz_to_freq_bins, calculate_iou, parse_frequency_ranges, detect_occupied_segments, freq_bins_to_mhz

class SpectrumDetectionLoss(nn.Module):
    """频谱检测专用损失函数 - 理论正确且数值稳定"""

    def __init__(self,
                 focal_alpha=0.25,
                 focal_gamma=2.0,
                 dice_weight=0.3,
                 boundary_weight=0.1,
                 class_weights=None):
        super(SpectrumDetectionLoss, self).__init__()
        self.focal_alpha = focal_alpha
        self.focal_gamma = focal_gamma
        self.dice_weight = dice_weight
        self.boundary_weight = boundary_weight

        # 类别权重处理不平衡
        if class_weights is not None:
            self.register_buffer('class_weights', torch.tensor(class_weights))
        else:
            self.class_weights = None

    def focal_loss(self, predictions, targets):
        """修复的Focal Loss实现"""
        # 计算BCE损失
        bce_loss = F.binary_cross_entropy_with_logits(predictions, targets, reduction='none')

        # 计算pt (正确的方式)
        p = torch.sigmoid(predictions)
        pt = targets * p + (1 - targets) * (1 - p)

        # 计算alpha权重
        alpha = targets * self.focal_alpha + (1 - targets) * (1 - self.focal_alpha)

        # 计算focal权重
        focal_weight = alpha * (1 - pt) ** self.focal_gamma

        # 应用focal权重
        focal_loss = focal_weight * bce_loss

        return focal_loss.mean()

    def dice_loss(self, predictions, targets, smooth=1e-6):
        """Dice损失 - 处理类别不平衡，关注重叠区域"""
        probs = torch.sigmoid(predictions)

        # 计算Dice系数
        intersection = (probs * targets).sum(dim=1)
        union = probs.sum(dim=1) + targets.sum(dim=1)

        dice = (2.0 * intersection + smooth) / (union + smooth)

        # 返回Dice损失
        return 1.0 - dice.mean()

    def boundary_aware_loss(self, predictions, targets):
        """边界感知损失 - 简化且高效的实现"""
        probs = torch.sigmoid(predictions)

        # 计算梯度 (边界检测)
        grad_pred = torch.abs(probs[:, 1:] - probs[:, :-1])
        grad_target = torch.abs(targets[:, 1:] - targets[:, :-1])

        # 边界区域的损失权重更高
        boundary_mask = (grad_target > 0.5).float()
        boundary_loss = F.mse_loss(grad_pred, grad_target, reduction='none')

        # 加权边界损失
        weighted_boundary_loss = boundary_loss * (1.0 + 2.0 * boundary_mask)

        return weighted_boundary_loss.mean()

    def forward(self, predictions, targets):
        """组合损失计算"""
        # 主要损失：Focal Loss (处理类别不平衡)
        focal = self.focal_loss(predictions, targets)

        # 辅助损失：Dice Loss (关注重叠区域)
        dice = self.dice_loss(predictions, targets)

        # 边界损失：提升边界精度
        boundary = self.boundary_aware_loss(predictions, targets)

        # 组合损失 (权重经过理论分析)
        total_loss = focal + self.dice_weight * dice + self.boundary_weight * boundary

        return total_loss

class MultiTaskLossManager(nn.Module):
    """多任务损失管理器 - 解决任务冲突和权重平衡"""

    def __init__(self, task_weights=None, adaptive_weights=True):
        super(MultiTaskLossManager, self).__init__()

        # 任务特定损失函数
        self.occupancy_loss = SpectrumDetectionLoss(
            focal_alpha=0.25, focal_gamma=2.0, dice_weight=0.3, boundary_weight=0.1
        )
        self.interference_loss = SpectrumDetectionLoss(
            focal_alpha=0.3, focal_gamma=2.5, dice_weight=0.4, boundary_weight=0.15  # 更敏感
        )
        self.overlap_loss = nn.CrossEntropyLoss(label_smoothing=0.1)  # 多类别分类

        # 任务权重
        if task_weights is None:
            task_weights = {'occupancy': 1.0, 'interference': 0.8, 'overlap': 0.6}

        self.register_buffer('task_weights', torch.tensor(list(task_weights.values())))
        self.task_names = list(task_weights.keys())

        # 自适应权重 (可选)
        self.adaptive_weights = adaptive_weights
        if adaptive_weights:
            self.weight_params = nn.Parameter(torch.ones(len(task_weights)))

    def compute_adaptive_weights(self, losses):
        """计算自适应任务权重"""
        if not self.adaptive_weights:
            return self.task_weights

        # 使用uncertainty weighting方法
        weights = torch.exp(-self.weight_params)
        regularization = 0.5 * torch.sum(self.weight_params)

        return weights, regularization

    def forward(self, predictions_dict, targets_dict):
        """计算多任务损失"""
        losses = {}
        total_loss = 0.0

        # 计算各任务损失
        if 'occupancy' in predictions_dict:
            losses['occupancy'] = self.occupancy_loss(
                predictions_dict['occupancy'], targets_dict['occupancy']
            )

        if 'interference' in predictions_dict:
            losses['interference'] = self.interference_loss(
                predictions_dict['interference'], targets_dict['interference']
            )

        if 'overlap' in predictions_dict:
            # 重叠检测需要调整维度: (batch, freq_bins, 4) -> (batch*freq_bins, 4)
            overlap_pred = predictions_dict['overlap']
            overlap_target = targets_dict['overlap']

            # 使用reshape而不是view，更安全
            overlap_pred_reshaped = overlap_pred.reshape(-1, overlap_pred.size(-1))  # (batch*freq_bins, 4)
            overlap_target_reshaped = overlap_target.reshape(-1)  # (batch*freq_bins,)

            losses['overlap'] = self.overlap_loss(
                overlap_pred_reshaped, overlap_target_reshaped
            )

        # 应用任务权重
        if self.adaptive_weights:
            weights, reg_loss = self.compute_adaptive_weights(losses)
            for i, (task, loss) in enumerate(losses.items()):
                total_loss += weights[i] * loss
            total_loss += reg_loss
        else:
            for i, (task, loss) in enumerate(losses.items()):
                if i < len(self.task_weights):
                    total_loss += self.task_weights[i] * loss

        return total_loss, losses

def create_target_labels(frequency_ranges: List[List[Tuple[float, float]]],
                        batch_size: int) -> torch.Tensor:
    """从频率范围创建目标标签"""
    targets = torch.zeros(batch_size, Config.FREQ_BINS)
    
    for i, ranges in enumerate(frequency_ranges):
        for start_freq, end_freq in ranges:
            start_bin = mhz_to_freq_bins(np.array([start_freq]), 
                                       Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
            end_bin = mhz_to_freq_bins(np.array([end_freq]), 
                                     Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
            
            start_bin = max(0, min(start_bin, Config.FREQ_BINS - 1))
            end_bin = max(0, min(end_bin, Config.FREQ_BINS - 1))
            
            if start_bin <= end_bin:
                targets[i, start_bin:end_bin+1] = 1.0
    
    return targets

def create_interference_targets(frequency_ranges: List[List[Tuple[float, float]]], 
                               batch_size: int, overlap_level: int = 2) -> torch.Tensor:
    """重叠计数法：从频率范围创建干扰检测目标标签，支持多重重叠"""
    targets = torch.zeros(batch_size, Config.FREQ_BINS)
    for i, ranges in enumerate(frequency_ranges):
        overlap_count = np.zeros(Config.FREQ_BINS, dtype=int)
        for start_freq, end_freq in ranges:
            start_bin = mhz_to_freq_bins(np.array([start_freq]), Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
            end_bin = mhz_to_freq_bins(np.array([end_freq]), Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
            start_bin = max(0, min(start_bin, Config.FREQ_BINS - 1))
            end_bin = max(0, min(end_bin, Config.FREQ_BINS - 1))
            if start_bin <= end_bin:
                overlap_count[start_bin:end_bin+1] += 1
        # 干扰区间：重叠数>=overlap_level
        targets[i, overlap_count >= overlap_level] = 1.0
    return targets

def create_overlap_targets(frequency_ranges: List[List[Tuple[float, float]]], batch_size: int) -> torch.Tensor:
    """生成0/1/2/3+四类重叠标签, 返回 long 张量"""
    targets = torch.zeros(batch_size, Config.FREQ_BINS, dtype=torch.long)
    for i, ranges in enumerate(frequency_ranges):
        overlap_counter = torch.zeros(Config.FREQ_BINS, dtype=torch.long)
        for start_f, end_f in ranges:
            s_bin = mhz_to_freq_bins(np.array([start_f]), Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
            e_bin = mhz_to_freq_bins(np.array([end_f]), Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
            s_bin = max(0, min(s_bin, Config.FREQ_BINS-1))
            e_bin = max(0, min(e_bin, Config.FREQ_BINS-1))
            if s_bin<=e_bin:
                overlap_counter[s_bin:e_bin+1] += 1
        overlap_counter[overlap_counter>=3] = 3
        targets[i] = overlap_counter
    return targets

def safe_backward_and_step(loss, model, optimizer, scaler=None, max_grad_norm=1.0):
    """改进的反向传播和参数更新 - 更合理的梯度处理"""
    # 检查损失是否有效 (放宽限制)
    if torch.isnan(loss) or torch.isinf(loss) or loss.item() > 100.0:
        print(f"⚠️ 跳过异常损失: {loss.item():.4f}")
        optimizer.zero_grad()
        return False

    if scaler is not None:
        # 混合精度训练
        scaler.scale(loss).backward()
        scaler.unscale_(optimizer)

        # 计算梯度范数
        grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)

        # 更宽松的梯度检查
        if torch.isnan(grad_norm) or torch.isinf(grad_norm):
            print(f"⚠️ 跳过NaN/Inf梯度: {grad_norm:.4f}")
            optimizer.zero_grad()
            scaler.update()
            return False
        elif grad_norm > 10.0:  # 只在梯度非常大时才跳过
            print(f"⚠️ 梯度过大但继续训练: {grad_norm:.4f}")
            # 不跳过，让梯度裁剪处理

        scaler.step(optimizer)
        scaler.update()
    else:
        # 标准训练
        loss.backward()

        # 计算梯度范数
        grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)

        # 更宽松的梯度检查
        if torch.isnan(grad_norm) or torch.isinf(grad_norm):
            print(f"⚠️ 跳过NaN/Inf梯度: {grad_norm:.4f}")
            optimizer.zero_grad()
            return False
        elif grad_norm > 10.0:  # 只在梯度非常大时才警告
            print(f"⚠️ 梯度过大但继续训练: {grad_norm:.4f}")
            # 不跳过，让梯度裁剪处理

        optimizer.step()

    return True

def process_single_prediction(power_spectrum, task_type, frequency_ranges, model, device, batch_idx, train_mode=False):
    """统一的单任务前向 + 目标生成函数。

    返回:
        predictions  – 模型输出 logits
        targets      – 已转移到 device 的目标张量
    """

    batch_size = power_spectrum.size(0)

    # 根据任务类型前向 + 目标生成
    if task_type == "overlap":
        # 4 类重叠检测 – CrossEntropy
        if Config.USE_AMP and device.type == 'cuda':
            with autocast('cuda'):
                predictions, _, _ = model(power_spectrum, "overlap")
        else:
            predictions, _, _ = model(power_spectrum, "overlap")
        targets = create_overlap_targets(frequency_ranges, batch_size)

    elif task_type == "interference":
        # 二分类干扰 – BCE
        if Config.USE_AMP and device.type == 'cuda':
            with autocast('cuda'):
                predictions, _, _ = model(power_spectrum, "interference")
        else:
            predictions, _, _ = model(power_spectrum, "interference")
        targets = create_interference_targets(frequency_ranges, batch_size).float()

    else:  # "occupancy"
        # 二分类占用 – BCE
        if Config.USE_AMP and device.type == 'cuda':
            with autocast('cuda'):
                predictions, _, _ = model(power_spectrum, "occupancy")
        else:
            predictions, _, _ = model(power_spectrum, "occupancy")
        if train_mode:
            threshold = np.random.uniform(0.2, 0.5)
            targets = (create_target_labels(frequency_ranges, batch_size) > threshold).float()
        else:
            targets = create_target_labels(frequency_ranges, batch_size).float()

    return predictions, targets.to(device)

def train_epoch_improved(model: SpectrumDetector, dataloader, loss_manager, optimizer,
                        device: torch.device, epoch: int, scheduler=None, scaler: GradScaler = None) -> Dict[str, float]:
    """改进的训练函数 - 使用新的损失管理器"""
    model.train()
    total_loss = 0.0
    total_samples = 0
    successful_updates = 0
    task_losses = {'occupancy': 0.0, 'interference': 0.0, 'overlap': 0.0}

    pbar = tqdm(dataloader, desc=f"训练 Epoch {epoch+1}")

    for batch_idx, batch in enumerate(pbar):
        power_spectrum = batch['power_spectrum'].to(device)
        frequency_ranges = batch['labels']
        batch_size = power_spectrum.size(0)

        optimizer.zero_grad()

        try:
            # 准备多任务预测和目标
            predictions_dict = {}
            targets_dict = {}

            # 占用检测
            if Config.USE_AMP and device.type == 'cuda':
                with autocast('cuda', dtype=torch.float16):
                    occ_pred, _, _ = model(power_spectrum, "occupancy")
                # 确保输出为float32用于损失计算
                occ_pred = occ_pred.float()
            else:
                occ_pred, _, _ = model(power_spectrum, "occupancy")

            predictions_dict['occupancy'] = occ_pred
            targets_dict['occupancy'] = create_target_labels(frequency_ranges, batch_size).to(device).float()

            # 干扰检测
            if Config.USE_AMP and device.type == 'cuda':
                with autocast('cuda', dtype=torch.float16):
                    int_pred, _, _ = model(power_spectrum, "interference")
                # 确保输出为float32用于损失计算
                int_pred = int_pred.float()
            else:
                int_pred, _, _ = model(power_spectrum, "interference")

            predictions_dict['interference'] = int_pred
            targets_dict['interference'] = create_interference_targets(frequency_ranges, batch_size).to(device).float()

            # 重叠检测 (可选，根据需要启用)
            if np.random.random() < 0.3:  # 降低到30%概率，减少复杂性
                if Config.USE_AMP and device.type == 'cuda':
                    with autocast('cuda', dtype=torch.float16):
                        ov_pred, _, _ = model(power_spectrum, "overlap")
                    # 确保输出为float32用于损失计算
                    ov_pred = ov_pred.float()
                else:
                    ov_pred, _, _ = model(power_spectrum, "overlap")

                # 重叠检测的维度应该是 (batch, freq_bins, 4)
                if len(ov_pred.shape) == 3 and ov_pred.shape[1] == 4:
                    # 如果是 (batch, 4, freq_bins)，转换为 (batch, freq_bins, 4)
                    predictions_dict['overlap'] = ov_pred.transpose(1, 2)
                else:
                    # 如果已经是 (batch, freq_bins, 4)，直接使用
                    predictions_dict['overlap'] = ov_pred
                targets_dict['overlap'] = create_overlap_targets(frequency_ranges, batch_size).to(device).long()

            # 数值稳定性检查
            for task, pred in predictions_dict.items():
                if torch.isnan(pred).any() or torch.isinf(pred).any():
                    print(f"⚠️ {task}预测包含NaN/Inf，跳过此批次")
                    optimizer.zero_grad()
                    continue

            for task, target in targets_dict.items():
                if torch.isnan(target).any() or torch.isinf(target).any():
                    print(f"⚠️ {task}目标包含NaN/Inf，跳过此批次")
                    optimizer.zero_grad()
                    continue

            # 计算多任务损失
            total_loss_batch, individual_losses = loss_manager(predictions_dict, targets_dict)

            # 额外的损失检查
            if torch.isnan(total_loss_batch) or torch.isinf(total_loss_batch):
                print(f"⚠️ 损失计算后出现NaN/Inf，跳过此批次")
                optimizer.zero_grad()
                continue

            # 反向传播
            success = safe_backward_and_step(total_loss_batch, model, optimizer, scaler, max_grad_norm=1.0)

            if success:
                successful_updates += 1
                total_loss += total_loss_batch.item() * batch_size
                total_samples += batch_size

                # 记录各任务损失
                for task, loss_val in individual_losses.items():
                    task_losses[task] += loss_val.item() * batch_size

                # 只有成功更新后才调整学习率
                if scheduler is not None:
                    scheduler.step()

        except Exception as e:
            print(f"⚠️ 训练过程中发生异常: {e}")
            optimizer.zero_grad()
            continue

        # 更新进度条
        if total_samples > 0:
            avg_loss = total_loss / total_samples
            success_rate = successful_updates / (batch_idx + 1)
            pbar.set_postfix({
                'loss': f'{total_loss_batch.item():.4f}' if 'total_loss_batch' in locals() else 'N/A',
                'avg_loss': f'{avg_loss:.4f}',
                'success': f'{success_rate:.2f}',
            })

    # 计算平均任务损失
    avg_task_losses = {}
    for task in task_losses:
        avg_task_losses[task] = task_losses[task] / total_samples if total_samples > 0 else 0.0

    return {
        'loss': total_loss / total_samples if total_samples > 0 else float('inf'),
        'success_rate': successful_updates / len(dataloader) if len(dataloader) > 0 else 0.0,
        'task_losses': avg_task_losses
    }

def calculate_interference_intervals(occupancy_intervals: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
    """计算干扰区间的辅助函数"""
    interference_intervals = []
    if len(occupancy_intervals) >= 2:
        sorted_ranges = sorted(occupancy_intervals, key=lambda x: x[0])
        for i, r1 in enumerate(sorted_ranges):
            for j, r2 in enumerate(sorted_ranges):
                if i >= j:
                    continue
                overlap_start = max(r1[0], r2[0])
                overlap_end = min(r1[1], r2[1])
                if overlap_start < overlap_end:
                    interference_intervals.append((overlap_start, overlap_end))
    
    # 合并重叠的干扰区间
    return parse_frequency_ranges(str(interference_intervals))

def convert_bins_to_mhz(segments_bins: List[Tuple[int, int]]) -> List[Tuple[float, float]]:
    """将bin区间转换为MHz区间的辅助函数"""
    if not segments_bins:
        return []
    
    start_bins = np.array([s[0] for s in segments_bins])
    end_bins = np.array([s[1] for s in segments_bins])
    start_freqs = freq_bins_to_mhz(start_bins, Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)
    end_freqs = freq_bins_to_mhz(end_bins, Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)
    
    return list(zip(start_freqs, end_freqs))

def validate_epoch_stable(model: SpectrumDetector, dataloader, criterion, device: torch.device) -> Dict[str, float]:
    """优化的验证函数"""
    model.eval()
    total_loss = 0.0
    total_samples = 0
    
    occupancy_iou_total = 0.0
    interference_iou_total = 0.0
    occupancy_samples = 0
    interference_samples = 0
    
    observation_window_val = (float(Config.FREQ_MIN), float(Config.FREQ_MAX))
    min_segment_width_bins = max(1, int(Config.MIN_SEGMENT_WIDTH * Config.FREQ_BINS / (Config.FREQ_MAX - Config.FREQ_MIN)))

    pbar = tqdm(dataloader, desc="验证中")
    
    # 新增评估指标统计
    recall_total = 0.0
    precision_total = 0.0
    boundary_error_total = 0.0
    small_recall_total = 0.0
    small_count = 0
    
    ce_loss = nn.CrossEntropyLoss()
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(pbar):
            power_spectrum = batch['power_spectrum'].to(device)
            true_frequency_ranges_batch = batch['labels'] 
            batch_size = power_spectrum.size(0)
            
            try:
                # 分别测试两种任务 - 修复调用方式
                occ_predictions_logits, _ = process_single_prediction(
                    power_spectrum, "occupancy", true_frequency_ranges_batch, model, device, batch_idx, False
                )
                ov_predictions_logits, _ = process_single_prediction(
                    power_spectrum, "overlap", true_frequency_ranges_batch, model, device, batch_idx, False
                )
                int_predictions_logits, _ = process_single_prediction(
                    power_spectrum, "interference", true_frequency_ranges_batch, model, device, batch_idx, False
                )
                
                # 创建目标损失计算用的二进制标签
                occ_targets_for_loss = create_target_labels(true_frequency_ranges_batch, batch_size).to(device)
                ov_targets_for_loss = create_overlap_targets(true_frequency_ranges_batch, batch_size).to(device)
                int_targets_for_loss = create_interference_targets(true_frequency_ranges_batch, batch_size).to(device)
                
                # 计算损失
                occ_loss = criterion(occ_predictions_logits, occ_targets_for_loss)

                # 修复重叠检测的维度问题
                if len(ov_predictions_logits.shape) == 3:
                    # 如果是 (batch, freq_bins, 4)，需要重塑为 (batch*freq_bins, 4)
                    ov_pred_reshaped = ov_predictions_logits.reshape(-1, ov_predictions_logits.size(-1))
                    ov_target_reshaped = ov_targets_for_loss.reshape(-1)
                    ov_loss = ce_loss(ov_pred_reshaped, ov_target_reshaped)
                else:
                    # 如果维度已经正确，直接计算
                    ov_loss = ce_loss(ov_predictions_logits, ov_targets_for_loss)

                int_loss = criterion(int_predictions_logits, int_targets_for_loss)
                loss = 0.6 * occ_loss + 0.4 * ov_loss + 0.3 * int_loss
                
                if not (torch.isnan(loss) or torch.isinf(loss)):
                    total_loss += loss.item() * batch_size
                    total_samples += batch_size
                
                # IoU 计算
                occ_pred_prob = torch.sigmoid(occ_predictions_logits).cpu().numpy()
                ov_pred_prob = torch.sigmoid(ov_predictions_logits).cpu().numpy()
                int_pred_prob = torch.sigmoid(int_predictions_logits).cpu().numpy()

                for i in range(batch_size):
                    true_occupancy_intervals = true_frequency_ranges_batch[i]
                    true_interference_intervals = calculate_interference_intervals(true_occupancy_intervals)

                    # 占用检测IoU
                    pred_occupancy_segments_bins = detect_occupied_segments(
                        power_spectrum=occ_pred_prob[i], 
                        threshold=Config.DETECTION_THRESHOLD,
                        min_width=min_segment_width_bins 
                    )
                    pred_occupancy_intervals_mhz = convert_bins_to_mhz(pred_occupancy_segments_bins)
                    
                    occ_iou = calculate_iou(pred_occupancy_intervals_mhz, true_occupancy_intervals, observation_window_val)
                    occupancy_iou_total += occ_iou
                    occupancy_samples += 1
                    
                    # 干扰检测IoU
                    pred_interference_segments_bins = detect_occupied_segments(
                        power_spectrum=int_pred_prob[i],
                        threshold=Config.OVERLAP_THRESHOLD,
                        min_width=min_segment_width_bins
                    )
                    pred_interference_intervals_mhz = convert_bins_to_mhz(pred_interference_segments_bins)

                    # 计算干扰IoU
                    if not true_interference_intervals and not pred_interference_intervals_mhz:
                        int_iou = 1.0
                    elif not true_interference_intervals and pred_interference_intervals_mhz:
                        int_iou = 0.0
                    else:
                        int_iou = calculate_iou(pred_interference_intervals_mhz, true_interference_intervals, observation_window_val)
                    
                    interference_iou_total += int_iou
                    interference_samples += 1
                
                # 计算Recall/Precision
                def calc_recall_precision(pred_intervals, true_intervals, window):
                    hit = 0
                    for p in pred_intervals:
                        for t in true_intervals:
                            iou = calculate_iou([p], [t], window)
                            if iou > 0.5:
                                hit += 1
                                break
                    recall = hit / (len(true_intervals) + 1e-8)
                    precision = hit / (len(pred_intervals) + 1e-8)
                    return recall, precision
                recall, precision = calc_recall_precision(pred_occupancy_intervals_mhz, true_occupancy_intervals, observation_window_val)
                recall_total += recall
                precision_total += precision
                # 小目标Recall
                small_targets = [t for t in true_occupancy_intervals if (t[1]-t[0]) < 3.0]
                small_preds = [p for p in pred_occupancy_intervals_mhz if (p[1]-p[0]) < 3.0]
                small_hit = 0
                for t in small_targets:
                    for p in small_preds:
                        iou = calculate_iou([p], [t], observation_window_val)
                        if iou > 0.5:
                            small_hit += 1
                            break
                if small_targets:
                    small_recall_total += small_hit / len(small_targets)
                    small_count += 1
                # 边界误差
                def calc_boundary_error(pred_intervals, true_intervals):
                    errors = []
                    for p in pred_intervals:
                        dists = [min(abs(p[0]-t[0]), abs(p[1]-t[1])) for t in true_intervals]
                        if dists:
                            errors.append(min(dists))
                    return np.mean(errors) if errors else 0
                boundary_error_total += calc_boundary_error(pred_occupancy_intervals_mhz, true_occupancy_intervals)
                
            except Exception as e:
                print(f"⚠️ 验证过程中发生异常: {e}")
                import traceback
                traceback.print_exc()
                continue

            # 更新进度条
            if total_samples > 0:
                avg_loss = total_loss / total_samples
                avg_occ_iou = occupancy_iou_total / occupancy_samples if occupancy_samples > 0 else 0
                avg_int_iou = interference_iou_total / interference_samples if interference_samples > 0 else 0
                
                pbar.set_postfix({
                    'loss': f'{avg_loss:.4f}',
                    'occ_iou': f'{avg_occ_iou:.3f}',
                    'int_iou': f'{avg_int_iou:.3f}'
                })
    
    return {
        'loss': total_loss / total_samples if total_samples > 0 else float('inf'),
        'occupancy_iou': occupancy_iou_total / occupancy_samples if occupancy_samples > 0 else 0.0,
        'interference_iou': interference_iou_total / interference_samples if interference_samples > 0 else 0.0,
        'overall_iou': (occupancy_iou_total + interference_iou_total) / (occupancy_samples + interference_samples) if (occupancy_samples + interference_samples) > 0 else 0.0,
        'recall': recall_total / occupancy_samples if occupancy_samples > 0 else 0.0,
        'precision': precision_total / occupancy_samples if occupancy_samples > 0 else 0.0,
        'small_recall': small_recall_total / small_count if small_count > 0 else 0.0,
        'boundary_error': boundary_error_total / occupancy_samples if occupancy_samples > 0 else 0.0
    }

def check_amp_availability(device: torch.device) -> bool:
    """
    检查自动混合精度训练是否可用
    """
    if device.type != 'cuda':
        return False
    
    # 检查PyTorch版本是否支持AMP
    if not hasattr(torch.amp, 'autocast'):
        return False
    
    # 检查GPU是否支持Tensor Cores (Volta架构及以上)
    if torch.cuda.is_available():
        major, minor = torch.cuda.get_device_capability()
        # Volta (7.0), Turing (7.5), Ampere (8.0+) 支持Tensor Cores
        return major >= 7
    
    return False

def train_model_stable(save_path: str = "model_stable.pth") -> SpectrumDetector:
    """
    数值稳定的训练主函数
    专注于解决梯度爆炸问题
    """
    print("=" * 60)
    print("🛡️ 数值稳定版频谱检测模型训练")
    print("🎯 专注解决梯度爆炸和训练不稳定问题")
    print("=" * 60)
    
    # 设置设备
    device = torch.device(Config.DEVICE)
    print(f"🔧 使用设备: {device}")
    
    if device.type == 'cuda':
        print(f"🎮 GPU型号: {torch.cuda.get_device_name()}")
        print(f"💾 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        # GPU优化
        torch.backends.cudnn.benchmark = True
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
        # 清空显存缓存
        torch.cuda.empty_cache()
        print(f"🧹 显存缓存已清空")
    
    # 加载数据
    print("📊 加载训练数据...")
    train_dataset = load_train_data()
    train_loader, val_loader = create_train_dataloaders(train_dataset)
    
    # 创建模型
    print("🧠 创建模型...")
    model = SpectrumDetector().to(device)
    print(f"⚙️ 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建改进的损失管理器
    loss_manager = MultiTaskLossManager(
        task_weights={'occupancy': 1.0, 'interference': 0.8, 'overlap': 0.6},
        adaptive_weights=True  # 启用自适应权重
    ).to(device)
    
    print(f"🎯 改进的损失函数配置:")
    print(f"   - 多任务损失管理器: ✅")
    print(f"   - 自适应权重: {loss_manager.adaptive_weights}")
    print(f"   - 任务权重: {dict(zip(loss_manager.task_names, loss_manager.task_weights.tolist()))}")
    print(f"   - Focal Loss + Dice Loss + 边界感知损失")
    
    # 保守的优化器设置
    optimizer = optim.AdamW(
        model.parameters(), 
        lr=Config.LEARNING_RATE * 0.5,  # 降低基础学习率
        weight_decay=1e-4,
        eps=1e-8,  # 增加数值稳定性
        amsgrad=True  # 使用AMSGrad变种，更稳定
    )
    
    # 更保守的学习率调度器
    total_steps = len(train_loader) * Config.NUM_EPOCHS
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=Config.LEARNING_RATE * 1.5,  # 大幅降低峰值学习率 (从4倍降到1.5倍)
        total_steps=total_steps,
        pct_start=0.3,                      # 30%时间用于warm-up
        anneal_strategy='cos',
        div_factor=20,                      # 更温和的起始学习率
        final_div_factor=2000              # 更低的最终学习率
    )
    
    print(f"📈 保守学习率策略:")
    print(f"   - 基础学习率: {Config.LEARNING_RATE * 0.5:.6f}")
    print(f"   - 峰值学习率: {Config.LEARNING_RATE * 1.5:.6f} (大幅降低)")
    print(f"   - 初始学习率: {Config.LEARNING_RATE * 1.5 / 20:.7f}")
    print(f"   - 最终学习率: {Config.LEARNING_RATE * 1.5 / 2000:.8f}")
    print(f"   - 预热比例: 30%")
    
    # 混合精度训练
    scaler = None
    use_amp = Config.USE_AMP and check_amp_availability(device)
    if use_amp:
        scaler = GradScaler(
            init_scale=2**10,  # 降低初始缩放因子，提高稳定性
            growth_factor=1.1,  # 更保守的增长因子
            backoff_factor=0.8,  # 更保守的回退因子
            growth_interval=1000  # 更长的增长间隔
        )
        print(f"✨ 启用保守混合精度训练")
        print(f"🛡️ 配置参数：初始缩放={2**10}, 增长因子=1.1, 回退因子=0.8")
    else:
        print("⚠️ 使用FP32训练")
    
    # 训练历史
    train_history = []
    val_history = []
    best_overall_iou = 0.0
    patience = 20  # 增加耐心值
    patience_counter = 0
    
    print("🎯 开始稳定训练...")
    start_time = time.time()
    
    for epoch in range(Config.NUM_EPOCHS):
        print(f"\n📅 Epoch {epoch+1}/{Config.NUM_EPOCHS}")
        print("-" * 40)
        
        # 训练
        train_metrics = train_epoch_improved(model, train_loader, loss_manager, optimizer, device, epoch, scheduler, scaler)
        train_history.append(train_metrics)

        # 验证 (暂时保持原有验证方式，后续可优化)
        val_metrics = validate_epoch_stable(model, val_loader, loss_manager.occupancy_loss, device)
        val_history.append(val_metrics)
        
        # 打印结果
        print(f"📊 训练损失: {train_metrics['loss']:.4f}")
        print(f"📊 验证损失: {val_metrics['loss']:.4f}")
        print(f"🎯 整体IoU: {val_metrics['overall_iou']:.4f}")
        print(f"🎯 占用检测IoU: {val_metrics['occupancy_iou']:.4f}")
        print(f"🎯 干扰检测IoU: {val_metrics['interference_iou']:.4f}")
        print(f"📈 学习率: {optimizer.param_groups[0]['lr']:.6f}")
        print(f"🎲 干扰训练概率: {train_metrics['success_rate']:.2f}")
        print(f"✅ 成功更新率: {train_metrics['success_rate']:.2f}")
        print(f"⚡ 混合精度: {'启用' if use_amp else '禁用'}")
        
        # 显示显存使用情况
        if device.type == 'cuda':
            memory_used = torch.cuda.memory_allocated() / 1e9
            memory_cached = torch.cuda.memory_reserved() / 1e9
            print(f"💾 显存使用: {memory_used:.1f}GB (缓存: {memory_cached:.1f}GB)")
        
        # 保存最佳模型
        current_iou = val_metrics['overall_iou']
        if current_iou > best_overall_iou:
            best_overall_iou = current_iou
            patience_counter = 0
            
            checkpoint_data = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'train_history': train_history,
                'val_history': val_history,
                'best_overall_iou': best_overall_iou,
                'use_amp': use_amp,
                'config': Config
            }
            
            if scaler is not None:
                checkpoint_data['scaler_state_dict'] = scaler.state_dict()
            
            torch.save(checkpoint_data, save_path)
            print(f"💾 保存最佳模型，整体IoU: {best_overall_iou:.4f}")
        else:
            patience_counter += 1
            
        # 检查训练稳定性
        if train_metrics['success_rate'] < 0.8:
            print(f"⚠️ 警告：成功更新率过低 ({train_metrics['success_rate']:.2f})")
            print("🔧 建议降低学习率或检查数据质量")
        
        # 早停
        if patience_counter >= patience:
            print(f"⏰ 早停：{patience} epochs无改善")
            break
    
    # 训练完成
    total_time = time.time() - start_time
    print(f"\n🎉 稳定训练完成！")
    print(f"⏱️ 总用时: {total_time:.2f}秒")
    print(f"🏆 最佳整体IoU: {best_overall_iou:.4f}")
    
    # 加载最佳模型
    if os.path.exists(save_path):
        checkpoint = torch.load(save_path)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"✅ 已加载最佳稳定模型")
    
    return model

if __name__ == "__main__":
    # 设置随机种子以便复现
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 开始稳定训练
    trained_model = train_model_stable()
    print("🎊 稳定训练完成！模型已保存为 model_stable.pth") 