import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import List, Tuple
from config import Config
from utils import (detect_occupied_segments, detect_interference_segments, 
                  freq_bins_to_mhz, mhz_to_freq_bins)

class PositionalEncoding(nn.Module):
    """位置编码模块，为Transformer提供位置信息"""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        return x + self.pe[:x.size(0), :]

class FrequencyDomainCNN(nn.Module):
    """频域CNN模块，专门处理频谱特征 - 增强感受野版本"""
    
    def __init__(self, input_size: int = Config.FREQ_BINS, embed_dim: int = 512):
        super(FrequencyDomainCNN, self).__init__()
        
        # 计算需要的感受野：20MHz需要至少20/0.048828 ≈ 410 bins
        # 目标感受野：500+ bins以确保充分覆盖
        
        # 多尺度频域特征提取 - 大幅增加感受野
        self.freq_conv_layers = nn.ModuleList([
            # 第一层：细粒度特征 + 初始感受野扩展
            nn.Sequential(
                nn.Conv1d(1, 64, kernel_size=7, padding=3, dilation=1),      # RF: 7
                nn.BatchNorm1d(64),
                nn.ReLU(inplace=True),
                nn.Conv1d(64, 64, kernel_size=7, padding=6, dilation=2),     # RF: 7+6*2=19
                nn.BatchNorm1d(64),
                nn.ReLU(inplace=True),
            ),
            # 第二层：中等尺度特征 + 空洞卷积
            nn.Sequential(
                nn.Conv1d(64, 128, kernel_size=15, padding=14, dilation=2),  # RF: 19+14*2=47
                nn.BatchNorm1d(128),
                nn.ReLU(inplace=True),
                nn.Conv1d(128, 128, kernel_size=15, padding=28, dilation=4), # RF: 47+14*4=103
                nn.BatchNorm1d(128),
                nn.ReLU(inplace=True),
            ),
            # 第三层：大尺度特征 + 更大空洞
            nn.Sequential(
                nn.Conv1d(128, 256, kernel_size=21, padding=40, dilation=4), # RF: 103+20*4=183
                nn.BatchNorm1d(256),
                nn.ReLU(inplace=True),
                nn.Conv1d(256, 256, kernel_size=21, padding=80, dilation=8), # RF: 183+20*8=343
                nn.BatchNorm1d(256),
                nn.ReLU(inplace=True),
            ),
            # 第四层：全局特征 + 极大感受野
            nn.Sequential(
                nn.Conv1d(256, 512, kernel_size=31, padding=120, dilation=8), # RF: 343+30*8=583
                nn.BatchNorm1d(512),
                nn.ReLU(inplace=True),
                nn.Conv1d(512, embed_dim, kernel_size=31, padding=240, dilation=16), # RF: 583+30*16=1063
                nn.BatchNorm1d(embed_dim),
                nn.ReLU(inplace=True),
            )
        ])
        
        # 残差连接 - 调整通道数匹配
        self.residual_convs = nn.ModuleList([
            nn.Conv1d(1, 64, kernel_size=1),      # 输入到第一层的残差
            nn.Conv1d(64, 128, kernel_size=1),    # 第一层到第二层的残差
            nn.Conv1d(128, 256, kernel_size=1),   # 第二层到第三层的残差
            nn.Conv1d(256, embed_dim, kernel_size=1)  # 第三层到第四层的残差
        ])
        
        # 频域注意力机制
        self.freq_attention = nn.MultiheadAttention(embed_dim, 8, batch_first=True)
        self.freq_norm = nn.LayerNorm(embed_dim)
        
        # 自适应池化到固定长度
        self.adaptive_pool = nn.AdaptiveAvgPool1d(input_size // 4)  # 降采样4倍
        
    def forward(self, x):
        if len(x.shape) == 2:
            x = x.unsqueeze(1)  # (batch, 1, freq_bins)
        
        residual = x # (batch, 1, 2048)
        
        # Apply layers with residual connections
        # Store the output of the last freq_conv_layer before adaptive pooling
        
        # Initial residual path and first conv block
        res_out = self.residual_convs[0](residual) 
        out = self.freq_conv_layers[0](x)
        out = out + res_out

        # Intermediate residual paths and conv blocks
        for i in range(1, len(self.freq_conv_layers)):
            res_out = self.residual_convs[i](out)
            out = self.freq_conv_layers[i](out)
            out = out + res_out
        
        # 'out' at this point has shape (batch, embed_dim, 2048)
        # This is the high-resolution feature map we want to skip connect
        high_res_features_for_skip = out 

        # Adaptive pooling and self-attention for Transformer input
        attn_input = self.adaptive_pool(out)  # (batch, embed_dim, input_size // 4)
        attn_input = attn_input.transpose(1, 2)  # (batch, input_size // 4, embed_dim)
        
        attn_out, _ = self.freq_attention(attn_input, attn_input, attn_input)
        final_cnn_output_for_transformer = self.freq_norm(attn_input + attn_out) # (batch, input_size // 4, embed_dim)
        
        # Return features for Transformer, and high-resolution features for skip connection
        return final_cnn_output_for_transformer, high_res_features_for_skip

class SpectrumTransformer(nn.Module):
    """频谱Transformer模块，建模长距离频域依赖"""
    
    def __init__(self, embed_dim: int = 512, num_heads: int = 8, num_layers: int = 6, 
                 ff_dim: int = 2048, dropout: float = 0.1):
        super(SpectrumTransformer, self).__init__()
        
        self.embed_dim = embed_dim
        self.pos_encoding = PositionalEncoding(embed_dim)
        
        # Transformer编码器层
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embed_dim,
            nhead=num_heads,
            dim_feedforward=ff_dim,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, 
            num_layers=num_layers
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(embed_dim)
        
    def forward(self, x):
        # 添加位置编码
        x = x.transpose(0, 1)  # (seq_len, batch, embed_dim) for positional encoding
        x = self.pos_encoding(x)
        x = x.transpose(0, 1)  # (batch, seq_len, embed_dim)
        
        # Transformer编码
        x = self.transformer_encoder(x)
        x = self.layer_norm(x)
        
        return x

class FreqCNNTransformer(nn.Module):
    """频域CNN + Transformer混合架构 (U-Net风格的跳跃连接)"""
    
    def __init__(self, input_size: int = Config.FREQ_BINS):
        super(FreqCNNTransformer, self).__init__()
        
        self.input_size = input_size
        embed_dim = Config.EMBED_DIM # 使用Config中的embed_dim
        
        # 频域CNN特征提取器 (现在返回两个输出：Transformer输入和高分辨率特征)
        self.freq_cnn = FrequencyDomainCNN(input_size, embed_dim)
        
        # Transformer建模序列依赖
        self.transformer = SpectrumTransformer(
            embed_dim=embed_dim,
            num_heads=Config.NUM_ATTENTION_HEADS,
            num_layers=Config.TRANSFORMER_LAYERS,
            ff_dim=Config.FF_DIM,
            dropout=Config.DROPOUT_RATE
        )
        
        # Decoder (Upsampling Path) - Modified for U-Net skip connections
        # Transformer output will be (batch, seq_len_after_pooling, embed_dim)
        # where seq_len_after_pooling = input_size // 4
        
        # First upsampling stage (2x upsampling)
        # From (batch, embed_dim, input_size // 4) to (batch, 256, input_size // 2)
        self.upconv1 = nn.ConvTranspose1d(embed_dim, 256, kernel_size=4, stride=2, padding=1)
        self.bn_up1 = nn.BatchNorm1d(256)
        self.relu_up1 = nn.ReLU(inplace=True)

        # Second upsampling stage (2x upsampling) - this is where we add the skip connection
        # From (batch, 256, input_size // 2) to (batch, 128, input_size)
        self.upconv2 = nn.ConvTranspose1d(256, 128, kernel_size=4, stride=2, padding=1)
        self.bn_up2 = nn.BatchNorm1d(128)
        self.relu_up2 = nn.ReLU(inplace=True)

        # Layer to process skip connection features to match channels before concatenation
        # high_res_features_for_skip will be (batch, embed_dim, input_size)
        # upconv2 output will be (batch, 128, input_size)
        self.skip_conv_adapt = nn.Conv1d(embed_dim, 128, kernel_size=1) # Adjust channels of skip connection

        # After concatenation, channels will be 128 + 128 = 256
        # Need a convolution to process these combined features before the final output layer
        self.final_conv_combined = nn.Sequential(
            nn.Conv1d(128 + 128, 64, kernel_size=3, padding=1), # 256 channels -> 64 channels
            nn.BatchNorm1d(64),
            nn.ReLU(inplace=True),
        )
        
        # Final output layer
        self.output_conv = nn.Conv1d(64, 1, kernel_size=1) # 64 channels -> 1 channel output

        # Adaptive resize is still needed as ConvTranspose1d can have slight output variations
        self.adaptive_resize = nn.AdaptiveAvgPool1d(input_size)
        
    def forward(self, x):
        if len(x.shape) == 2:
            x = x.unsqueeze(1)  # (batch, 1, input_size)
        # else: no need to squeeze if already (batch, 1, input_size)
        
        # FreqCNN现在返回两个东西：供Transformer用的特征 和 高分辨率特征
        cnn_features_for_transformer, high_res_cnn_features = self.freq_cnn(x) 
        # cnn_features_for_transformer: (batch, input_size // 4, embed_dim)
        # high_res_cnn_features: (batch, embed_dim, input_size) - 跳跃连接的来源

        # Transformer处理较低分辨率的特征
        transformer_out = self.transformer(cnn_features_for_transformer) 
        # (batch, input_size // 4, embed_dim)

        # 转置维度，以便ConvTranspose1d处理 (batch, embed_dim, seq_len)
        transformer_out = transformer_out.transpose(1, 2)  
        # (batch, embed_dim, input_size // 4)

        # 解码器路径
        # 阶段1: 上采样2倍 (从 input_size//4 到 input_size//2)
        upsampled = self.relu_up1(self.bn_up1(self.upconv1(transformer_out)))
        # (batch, 256, input_size // 2)

        # 阶段2: 上采样2倍 (从 input_size//2 到 input_size)
        upsampled = self.relu_up2(self.bn_up2(self.upconv2(upsampled)))
        # (batch, 128, input_size)

        # 处理跳跃连接特征，使其通道数与上采样特征匹配
        processed_skip_features = self.skip_conv_adapt(high_res_cnn_features)
        # (batch, 128, input_size)

        # 拼接上采样特征与处理后的跳跃连接特征 (U-Net风格)
        combined_features = torch.cat([upsampled, processed_skip_features], dim=1)
        # (batch, 128 + 128 = 256, input_size)

        # 最终卷积层，进一步融合拼接后的特征
        output = self.final_conv_combined(combined_features)
        # (batch, 64, input_size)
        
        # 最终输出层，得到单通道的概率图
        output = self.output_conv(output)
        # (batch, 1, input_size)
        output = output.squeeze(1) # 移除通道维度，得到 (batch, input_size)

        # 自适应调整到目标尺寸，以防ConvTranspose1d输出略有偏差
        output = self.adaptive_resize(output.unsqueeze(1)).squeeze(1)  # (batch, input_size)
        
        return output

class SpectrumDetector(nn.Module):
    """频谱检测器 - 基于频域CNN+Transformer架构"""
    
    def __init__(self):
        super(SpectrumDetector, self).__init__()
        
        # 主干网络
        self.backbone = FreqCNNTransformer(Config.FREQ_BINS)
        
        # 多任务检测头 (输出logits，不使用sigmoid)
        self.occupancy_head = nn.Sequential(
            nn.Linear(Config.FREQ_BINS, Config.FREQ_BINS),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(Config.FREQ_BINS, Config.FREQ_BINS)
        )
        
        self.interference_head = nn.Sequential(
            nn.Linear(Config.FREQ_BINS, Config.FREQ_BINS // 2),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(Config.FREQ_BINS // 2, Config.FREQ_BINS)
        )
        
        # 置信度估计头
        self.confidence_head = nn.Sequential(
            nn.Linear(Config.FREQ_BINS, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.05),
            nn.Linear(256, Config.FREQ_BINS),
            nn.Sigmoid()
        )
        
    def forward(self, power_spectrum, task_type="occupancy"):
        # 主干特征提取
        features = self.backbone(power_spectrum)
        
        # 任务特定检测
        if task_type == "occupancy":
            detection_prob = self.occupancy_head(features)
        elif task_type == "interference":
            detection_prob = self.interference_head(features)
        else:
            detection_prob = self.occupancy_head(features)
        
        # 置信度估计
        confidence = self.confidence_head(features)
        
        return detection_prob, confidence

class SpectrumAnalyzer:
    """频谱分析器 - 使用CNN+Transformer进行分析"""
    
    def __init__(self, model: SpectrumDetector = None, device: str = Config.DEVICE):
        self.device = device
        
        if model is None:
            self.model = SpectrumDetector().to(device)
        else:
            self.model = model.to(device)
            
        self.model.eval()
        
        # 高斯平滑核
        self.smooth_kernel = self._create_gaussian_kernel(Config.SMOOTHING_WINDOW)
        
        # 自适应阈值参数
        self.base_threshold_occ = 0.3       # 占用检测基础阈值
        self.base_threshold_int = 0.25      # 干扰检测基础阈值
        self.density_factor = 0.05          # 密度调整因子
        self.confidence_factor = 0.1        # 置信度调整因子
        self.freq_range_factor = 0.02       # 频率范围调整因子
    
    def _create_gaussian_kernel(self, window_size: int) -> torch.Tensor:
        """创建高斯平滑核"""
        sigma = window_size / 3.0
        x = torch.arange(window_size, dtype=torch.float32) - window_size // 2
        kernel = torch.exp(-0.5 * (x / sigma) ** 2)
        return kernel / kernel.sum()
    
    def _smooth_detection(self, detection: np.ndarray) -> np.ndarray:
        """平滑检测结果"""
        if len(detection) < len(self.smooth_kernel):
            return detection
            
        # 使用卷积进行平滑
        kernel = self.smooth_kernel.numpy()
        padded = np.pad(detection, len(kernel)//2, mode='edge')
        smoothed = np.convolve(padded, kernel, mode='valid')
        return smoothed
    
    def _estimate_signal_density(self, detection_prob: np.ndarray) -> float:
        """估计信号密度"""
        # 使用高于某个阈值的点的比例来估计密度
        signal_ratio = np.mean(detection_prob > 0.2)
        return signal_ratio
    
    def _compute_adaptive_threshold(self, detection_prob: np.ndarray, 
                                   confidence: np.ndarray, 
                                   freq_range: Tuple[float, float],
                                   task_type: str = "occupancy") -> float:
        """
        计算自适应阈值
        
        Args:
            detection_prob: 检测概率
            confidence: 置信度
            freq_range: 频率范围
            task_type: 任务类型
        
        Returns:
            adaptive_threshold: 自适应阈值
        """
        # 选择基础阈值
        base_threshold = self.base_threshold_occ if task_type == "occupancy" else self.base_threshold_int
        
        # 估计信号密度
        signal_density = self._estimate_signal_density(detection_prob)
        
        # 计算平均置信度
        avg_confidence = np.mean(confidence)
        
        # 计算频率范围因子 (范围越小，阈值越低，更敏感)
        freq_span = freq_range[1] - freq_range[0]
        freq_factor = max(0, (100 - freq_span) / 100) * self.freq_range_factor
        
        # 自适应调整
        # 1. 高信号密度 -> 降低阈值 (更容易检测到信号)
        # 2. 高置信度 -> 降低阈值 (模型更确定)
        # 3. 小频率范围 -> 降低阈值 (更精细检测)
        adaptive_threshold = (base_threshold - 
                            self.density_factor * signal_density - 
                            self.confidence_factor * (avg_confidence - 0.5) -
                            freq_factor)
        
        # 限制阈值范围
        if task_type == "occupancy":
            adaptive_threshold = np.clip(adaptive_threshold, 0.15, 0.55)
        else:  # interference
            adaptive_threshold = np.clip(adaptive_threshold, 0.1, 0.45)
        
        return adaptive_threshold
    
    def _apply_nms(self, segments: List[Tuple[float, float]], 
                   scores: List[float], threshold: float = 0.3) -> List[Tuple[float, float]]:
        """非最大抑制去除重叠检测"""
        if not segments:
            return segments
            
        # 按分数排序
        sorted_indices = sorted(range(len(scores)), key=lambda i: scores[i], reverse=True)
        
        keep = []
        for i in sorted_indices:
            seg_i = segments[i]
            keep_current = True
            
            for j in keep:
                seg_j = segments[j]
                # 计算重叠度
                overlap_start = max(seg_i[0], seg_j[0])
                overlap_end = min(seg_i[1], seg_j[1])
                
                if overlap_start < overlap_end:
                    overlap = overlap_end - overlap_start
                    union = (seg_i[1] - seg_i[0]) + (seg_j[1] - seg_j[0]) - overlap
                    iou = overlap / union if union > 0 else 0
                    
                    if iou > threshold:
                        keep_current = False
                        break
            
            if keep_current:
                keep.append(i)
        
        return [segments[i] for i in keep]
    
    def detect_occupancy(self, power_spectrum: torch.Tensor, 
                        freq_range: Tuple[float, float],
                        use_ml: bool = True) -> List[Tuple[float, float]]:
        """占用检测使用CNN+Transformer"""
        all_segments = []
        all_scores = []
        
        if use_ml and hasattr(self.model, 'eval'):
            with torch.no_grad():
                if len(power_spectrum.shape) == 1:
                    power_spectrum = power_spectrum.unsqueeze(0)
                
                power_spectrum = power_spectrum.to(self.device)
                detection_logits, confidence = self.model(power_spectrum, "occupancy")
                # 将logits转换为概率
                detection_prob = torch.sigmoid(detection_logits).cpu().numpy().squeeze()
                confidence = confidence.cpu().numpy().squeeze()
                
                # 平滑处理
                detection_smooth = self._smooth_detection(detection_prob)
                
                # 计算自适应阈值
                adaptive_threshold = self._compute_adaptive_threshold(
                    detection_smooth, confidence, freq_range, "occupancy"
                )
                
                # 使用自适应阈值进行检测
                segments_bins = detect_occupied_segments(detection_smooth, threshold=adaptive_threshold)
                
                for start_bin, end_bin in segments_bins:
                    start_freq = freq_bins_to_mhz(np.array([start_bin]), 
                                                Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
                    end_freq = freq_bins_to_mhz(np.array([end_bin]), 
                                              Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
                    
                    # 计算置信度分数
                    score = np.mean(confidence[start_bin:end_bin+1]) * np.mean(detection_smooth[start_bin:end_bin+1])
                    
                    # 动态置信度阈值 (基于自适应阈值调整)
                    confidence_threshold = max(0.12, adaptive_threshold + 0.03)
                    if score > confidence_threshold:
                        all_segments.append((start_freq, end_freq))
                        all_scores.append(score)
        
        # 应用非最大抑制
        if all_segments:
            all_segments = self._apply_nms(all_segments, all_scores, 0.25)
        
        # 过滤到指定频率范围
        filtered_segments = []
        for start_freq, end_freq in all_segments:
            if start_freq >= freq_range[0] and end_freq <= freq_range[1]:
                if end_freq - start_freq >= Config.MIN_SEGMENT_WIDTH:
                    filtered_segments.append((start_freq, end_freq))
            elif start_freq < freq_range[1] and end_freq > freq_range[0]:
                clipped_start = max(start_freq, freq_range[0])
                clipped_end = min(end_freq, freq_range[1])
                if clipped_end - clipped_start >= Config.MIN_SEGMENT_WIDTH:
                    filtered_segments.append((clipped_start, clipped_end))
        
        return filtered_segments
    
    def detect_interference(self, power_spectrum: torch.Tensor,
                          freq_range: Tuple[float, float],
                          use_ml: bool = True) -> List[Tuple[float, float]]:
        """干扰检测使用CNN+Transformer"""
        if use_ml and hasattr(self.model, 'eval'):
            with torch.no_grad():
                if len(power_spectrum.shape) == 1:
                    power_spectrum = power_spectrum.unsqueeze(0)
                
                power_spectrum = power_spectrum.to(self.device)
                detection_logits, confidence = self.model(power_spectrum, "interference")
                # 将logits转换为概率
                detection_prob = torch.sigmoid(detection_logits).cpu().numpy().squeeze()
                confidence = confidence.cpu().numpy().squeeze()
                
                # 平滑处理
                detection_smooth = self._smooth_detection(detection_prob)
                
                # 计算自适应阈值
                adaptive_threshold = self._compute_adaptive_threshold(
                    detection_smooth, confidence, freq_range, "interference"
                )
                
                # 检测干扰区域
                segments_bins = detect_occupied_segments(detection_smooth, threshold=adaptive_threshold)
                
                interference_segments = []
                for start_bin, end_bin in segments_bins:
                    start_freq = freq_bins_to_mhz(np.array([start_bin]), 
                                                Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
                    end_freq = freq_bins_to_mhz(np.array([end_bin]), 
                                              Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
                    
                    # 动态置信度过滤
                    score = np.mean(confidence[start_bin:end_bin+1])
                    confidence_threshold = max(0.3, adaptive_threshold + 0.15)
                    if score > confidence_threshold:
                        # 过滤到指定范围
                        if start_freq >= freq_range[0] and end_freq <= freq_range[1]:
                            if end_freq - start_freq >= Config.MIN_SEGMENT_WIDTH:
                                interference_segments.append((start_freq, end_freq))
                        elif start_freq < freq_range[1] and end_freq > freq_range[0]:
                            clipped_start = max(start_freq, freq_range[0])
                            clipped_end = min(end_freq, freq_range[1])
                            if clipped_end - clipped_start >= Config.MIN_SEGMENT_WIDTH:
                                interference_segments.append((clipped_start, clipped_end))
                
                return interference_segments
        
        # 传统方法兜底
        all_segments = self.detect_occupancy(power_spectrum, (Config.FREQ_MIN, Config.FREQ_MAX), False)
        interference_segments = []
        
        # 检测重叠区域
        for i, seg1 in enumerate(all_segments):
            for j, seg2 in enumerate(all_segments[i+1:], i+1):
                overlap_start = max(seg1[0], seg2[0])
                overlap_end = min(seg1[1], seg2[1])
                
                if overlap_start < overlap_end:
                    # 过滤到指定范围
                    if overlap_start >= freq_range[0] and overlap_end <= freq_range[1]:
                        if overlap_end - overlap_start >= Config.MIN_SEGMENT_WIDTH:
                            interference_segments.append((overlap_start, overlap_end))
        
        return interference_segments
    
    def analyze_spectrum(self, power_spectrum: torch.Tensor, 
                        task_type: str, 
                        freq_range: Tuple[float, float]) -> List[Tuple[float, float]]:
        """根据任务类型分析频谱"""
        if task_type == "occupancy":
            return self.detect_occupancy(power_spectrum, freq_range)
        elif task_type == "interference":
            return self.detect_interference(power_spectrum, freq_range)
        else:
            return self.detect_occupancy(power_spectrum, freq_range)

def create_model(pretrained_path: str = None) -> SpectrumAnalyzer:
    """创建分析模型"""
    detector = SpectrumDetector()
    
    if pretrained_path and torch.cuda.is_available():
        try:
            checkpoint = torch.load(pretrained_path)
            detector.load_state_dict(checkpoint['model_state_dict'])
            print(f"✅ 成功加载预训练模型: {pretrained_path}")
        except Exception as e:
            print(f"⚠️ 无法加载预训练模型: {e}，使用随机初始化")
    
    analyzer = SpectrumAnalyzer(detector)
    return analyzer 