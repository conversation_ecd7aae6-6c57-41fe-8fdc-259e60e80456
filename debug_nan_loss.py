#!/usr/bin/env python3
"""
NaN损失调试脚本
快速诊断损失函数中的数值不稳定问题
"""

import torch
import torch.nn.functional as F
import numpy as np
from config import Config
from train import SpectrumDetectionLoss, MultiTaskLossManager, create_target_labels, create_interference_targets, create_overlap_targets
from model import SpectrumDetector
from dataset import load_train_data, create_train_dataloaders

def debug_loss_function():
    """调试损失函数的数值稳定性"""
    print("🔍 调试损失函数数值稳定性")
    print("=" * 50)
    
    device = torch.device(Config.DEVICE)
    
    # 创建损失管理器
    loss_manager = MultiTaskLossManager(
        task_weights={'occupancy': 1.0, 'interference': 0.8, 'overlap': 0.6},
        adaptive_weights=True
    ).to(device)
    
    # 测试不同的输入场景
    batch_size = 4
    freq_bins = Config.FREQ_BINS
    
    test_cases = [
        ("正常随机输入", torch.randn(batch_size, freq_bins)),
        ("全零输入", torch.zeros(batch_size, freq_bins)),
        ("全一输入", torch.ones(batch_size, freq_bins)),
        ("极大值输入", torch.ones(batch_size, freq_bins) * 100),
        ("极小值输入", torch.ones(batch_size, freq_bins) * -100),
        ("混合极值", torch.cat([torch.ones(batch_size//2, freq_bins) * 100, 
                              torch.ones(batch_size//2, freq_bins) * -100], dim=0))
    ]
    
    for case_name, predictions in test_cases:
        print(f"\n📊 测试场景: {case_name}")
        
        try:
            predictions = predictions.to(device)
            targets = torch.randint(0, 2, (batch_size, freq_bins)).float().to(device)
            
            # 测试单个损失函数
            spectrum_loss = SpectrumDetectionLoss().to(device)
            loss = spectrum_loss(predictions, targets)
            
            print(f"   预测范围: [{predictions.min().item():.3f}, {predictions.max().item():.3f}]")
            print(f"   损失值: {loss.item():.6f}")
            
            if torch.isnan(loss) or torch.isinf(loss):
                print(f"   ❌ 检测到NaN/Inf损失")
                
                # 分别测试各个组件
                focal_loss = spectrum_loss.focal_loss(predictions, targets)
                dice_loss = spectrum_loss.dice_loss(predictions, targets)
                boundary_loss = spectrum_loss.boundary_aware_loss(predictions, targets)
                
                print(f"   - Focal Loss: {focal_loss.item():.6f}")
                print(f"   - Dice Loss: {dice_loss.item():.6f}")
                print(f"   - Boundary Loss: {boundary_loss.item():.6f}")
            else:
                print(f"   ✅ 损失正常")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")

def debug_model_output():
    """调试模型输出的数值稳定性"""
    print("\n🔍 调试模型输出数值稳定性")
    print("=" * 50)
    
    device = torch.device(Config.DEVICE)
    model = SpectrumDetector().to(device)
    model.eval()
    
    # 测试不同的输入
    batch_size = 2
    freq_bins = Config.FREQ_BINS
    
    test_inputs = [
        ("正常随机输入", torch.randn(batch_size, freq_bins)),
        ("全零输入", torch.zeros(batch_size, freq_bins)),
        ("极大值输入", torch.ones(batch_size, freq_bins) * 10),
        ("极小值输入", torch.ones(batch_size, freq_bins) * -10),
    ]
    
    for case_name, inputs in test_inputs:
        print(f"\n📊 测试场景: {case_name}")
        
        try:
            inputs = inputs.to(device)
            
            with torch.no_grad():
                # 测试各个任务
                for task in ["occupancy", "interference", "overlap"]:
                    output, confidence, _ = model(inputs, task_type=task)
                    
                    print(f"   {task}:")
                    print(f"     输出形状: {output.shape}")
                    print(f"     输出范围: [{output.min().item():.3f}, {output.max().item():.3f}]")
                    
                    if torch.isnan(output).any() or torch.isinf(output).any():
                        print(f"     ❌ 输出包含NaN/Inf")
                    else:
                        print(f"     ✅ 输出正常")
                        
        except Exception as e:
            print(f"   ❌ 异常: {e}")

def debug_real_data():
    """使用真实数据调试"""
    print("\n🔍 使用真实数据调试")
    print("=" * 50)
    
    try:
        device = torch.device(Config.DEVICE)
        
        # 加载少量真实数据
        train_dataset = load_train_data()
        train_loader, _ = create_train_dataloaders(train_dataset)
        
        model = SpectrumDetector().to(device)
        loss_manager = MultiTaskLossManager(
            task_weights={'occupancy': 1.0, 'interference': 0.8, 'overlap': 0.6},
            adaptive_weights=True
        ).to(device)
        
        # 测试第一个批次
        for batch_idx, batch in enumerate(train_loader):
            if batch_idx >= 3:  # 只测试前3个批次
                break
                
            print(f"\n📊 批次 {batch_idx + 1}:")
            
            power_spectrum = batch['power_spectrum'].to(device)
            frequency_ranges = batch['labels']
            batch_size = power_spectrum.size(0)
            
            print(f"   输入形状: {power_spectrum.shape}")
            print(f"   输入范围: [{power_spectrum.min().item():.3f}, {power_spectrum.max().item():.3f}]")
            
            # 检查输入是否有问题
            if torch.isnan(power_spectrum).any() or torch.isinf(power_spectrum).any():
                print(f"   ❌ 输入数据包含NaN/Inf")
                continue
            
            try:
                # 模型前向传播
                with torch.no_grad():
                    occ_pred, _, _ = model(power_spectrum, "occupancy")
                    int_pred, _, _ = model(power_spectrum, "interference")
                
                print(f"   占用预测范围: [{occ_pred.min().item():.3f}, {occ_pred.max().item():.3f}]")
                print(f"   干扰预测范围: [{int_pred.min().item():.3f}, {int_pred.max().item():.3f}]")
                
                # 创建目标
                occ_targets = create_target_labels(frequency_ranges, batch_size).to(device).float()
                int_targets = create_interference_targets(frequency_ranges, batch_size).to(device).float()
                
                print(f"   占用目标范围: [{occ_targets.min().item():.3f}, {occ_targets.max().item():.3f}]")
                print(f"   干扰目标范围: [{int_targets.min().item():.3f}, {int_targets.max().item():.3f}]")
                
                # 测试损失计算
                predictions_dict = {'occupancy': occ_pred, 'interference': int_pred}
                targets_dict = {'occupancy': occ_targets, 'interference': int_targets}
                
                total_loss, individual_losses = loss_manager(predictions_dict, targets_dict)
                
                print(f"   总损失: {total_loss.item():.6f}")
                for task, loss_val in individual_losses.items():
                    print(f"   {task}损失: {loss_val.item():.6f}")
                
                if torch.isnan(total_loss) or torch.isinf(total_loss):
                    print(f"   ❌ 检测到NaN/Inf损失")
                else:
                    print(f"   ✅ 损失正常")
                    
            except Exception as e:
                print(f"   ❌ 异常: {e}")
                import traceback
                traceback.print_exc()
                
    except Exception as e:
        print(f"❌ 真实数据测试失败: {e}")

def main():
    """主调试函数"""
    print("🚀 开始NaN损失调试")
    
    # 1. 调试损失函数
    debug_loss_function()
    
    # 2. 调试模型输出
    debug_model_output()
    
    # 3. 调试真实数据
    debug_real_data()
    
    print("\n" + "=" * 50)
    print("🎉 调试完成")
    print("\n建议:")
    print("1. 检查是否有NaN/Inf的输入数据")
    print("2. 检查模型权重是否正常初始化")
    print("3. 检查损失函数的数值稳定性")
    print("4. 考虑降低学习率或使用梯度裁剪")

if __name__ == "__main__":
    main()
