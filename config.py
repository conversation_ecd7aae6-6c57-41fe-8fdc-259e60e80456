import os
import torch

class Config:
    """配置类，包含所有超参数和路径设置"""
    
    # 数据路径
    TRAIN_DATA_PATH = "data_and_code/train.h5"
    TEST_DATA_PATH = "data_and_code/test_public.h5"
    OUTPUT_PATH = "predictions.txt"
    MODEL_SAVE_PATH = "model_stable.pth"
    
    # 频谱参数 - 优化分辨率
    FREQ_MIN = 2400  # MHz
    FREQ_MAX = 2500  # MHz
    SAMPLE_RATE = 100e6  # 100 MS/s
    FREQ_BINS = 2048  # 降低到2048，平衡精度和效率 (约0.098 MHz/bin)
    INPUT_LENGTH = 100000  # 采样点数
    FREQ_RESOLUTION = (FREQ_MAX - FREQ_MIN) / FREQ_BINS  # 约0.098 MHz/bin

    # 训练参数 - 4090 GPU优化 (24GB显存)
    BATCH_SIZE = 64  # 增大batch size，充分利用4090显存
    LEARNING_RATE = 2e-4  # 适当提高学习率
    NUM_EPOCHS = 80  # 增加训练轮数
    TRAIN_SPLIT = 0.85
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

    # 混合精度训练 - 4090原生支持，现在重新启用
    USE_AMP = True  # 重新启用，训练已稳定
    AMP_OPT_LEVEL = "O1"

    # 模型架构参数 - 为4090优化
    EMBED_DIM = 512  # 增加嵌入维度，利用4090算力
    NUM_ATTENTION_HEADS = 8  # 增加注意力头数
    TRANSFORMER_LAYERS = 6   # 适度增加层数
    FF_DIM = 2048            # 增加前馈维度
    DROPOUT_RATE = 0.1       # 降低dropout
    CNN_CHANNELS = [64, 128, 256, 512]  # 增加通道数
    
    # 检测参数
    DETECTION_THRESHOLD = 0.25
    MIN_SEGMENT_WIDTH = 1.0
    OVERLAP_THRESHOLD = 0.5
    SMOOTHING_WINDOW = 5
    MULTI_SCALE_KERNELS = [3, 5, 7, 11, 15]
    DILATED_RATES = [1, 2, 4, 8]
    
    # 高级检测策略
    USE_ENSEMBLE = True
    NMS_THRESHOLD = 0.35
    CONFIDENCE_THRESHOLD = 0.55
    
    # 数据增强参数 - 更保守和合理的配置
    AUGMENTATION_CONFIG = {
        'prob': 0.6,                        # 总概率 (降低)
        'noise_prob': 0.5,                  # 噪声概率 (降低)
        'amplitude_prob': 0.3,              # 幅度缩放概率 (降低)
        'phase_prob': 0.2,                  # 相位旋转概率 (降低)
        'freq_shift_prob': 0.15,            # 频率偏移概率 (大幅降低)
        'time_shift_prob': 0.1,             # 时域偏移概率 (降低)
        'interference_prob': 0.2,           # 干扰信号概率 (降低)
        'multipath_prob': 0.1,              # 多径衰落概率 (降低)
        
        # 参数范围 - 更保守的设置
        'snr_range': (15, 35),              # 信噪比范围 (dB) - 提高下限
        'amplitude_scale_range': (0.8, 1.2), # 幅度缩放范围 - 缩小范围
        'phase_rotation_max': 30.0,         # 最大相位旋转 (度) - 降低
        'freq_shift_max': 2.0,              # 最大频率偏移 (MHz) - 大幅降低
        'time_shift_max': 500,              # 最大时域偏移 (样本) - 降低
        'interference_count_range': (1, 3), # 干扰信号数量范围 - 降低
        'multipath_count': 2,               # 多径数量 - 降低
        'gaussian_sigma': 1.5               # 高斯平滑参数 - 降低
    }
    
    # 向后兼容的单独属性访问
    @property
    def AUGMENTATION_PROB(self): return self.AUGMENTATION_CONFIG['prob']
    @property
    def NOISE_PROB(self): return self.AUGMENTATION_CONFIG['noise_prob']
    @property
    def AMPLITUDE_PROB(self): return self.AUGMENTATION_CONFIG['amplitude_prob']
    @property
    def PHASE_PROB(self): return self.AUGMENTATION_CONFIG['phase_prob']
    @property
    def FREQ_SHIFT_PROB(self): return self.AUGMENTATION_CONFIG['freq_shift_prob']
    @property
    def TIME_SHIFT_PROB(self): return self.AUGMENTATION_CONFIG['time_shift_prob']
    @property
    def INTERFERENCE_PROB(self): return self.AUGMENTATION_CONFIG['interference_prob']
    @property
    def MULTIPATH_PROB(self): return self.AUGMENTATION_CONFIG['multipath_prob']
    @property
    def SNR_RANGE(self): return self.AUGMENTATION_CONFIG['snr_range']
    @property
    def AMPLITUDE_SCALE_RANGE(self): return self.AUGMENTATION_CONFIG['amplitude_scale_range']
    @property
    def PHASE_ROTATION_MAX(self): return self.AUGMENTATION_CONFIG['phase_rotation_max']
    @property
    def FREQ_SHIFT_MAX(self): return self.AUGMENTATION_CONFIG['freq_shift_max']
    @property
    def TIME_SHIFT_MAX(self): return self.AUGMENTATION_CONFIG['time_shift_max']
    @property
    def INTERFERENCE_COUNT_RANGE(self): return self.AUGMENTATION_CONFIG['interference_count_range']
    @property
    def MULTIPATH_COUNT(self): return self.AUGMENTATION_CONFIG['multipath_count']
    @property
    def GAUSSIAN_SIGMA(self): return self.AUGMENTATION_CONFIG['gaussian_sigma'] 