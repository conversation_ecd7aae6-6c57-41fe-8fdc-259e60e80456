import torch
import torch.nn as nn
import torch.optim as optim
from torch.amp import autocast, GradScaler
import numpy as np
import time
from tqdm import tqdm
from typing import Dict, List, Tuple
import os

from config import Config
from dataset import load_train_data, create_train_dataloaders
from model import SpectrumDetector, create_model
from utils import mhz_to_freq_bins, calculate_iou, parse_frequency_ranges, detect_occupied_segments, freq_bins_to_mhz

class StableSpectrumLoss(nn.Module):
    """数值稳定的频谱检测损失函数，专注于稳定性"""
    
    def __init__(self, focal_alpha=0.25, focal_gamma=1.0, smooth_weight=0.1):
        super(StableSpectrumLoss, self).__init__()
        self.focal_alpha = focal_alpha
        self.focal_gamma = focal_gamma
        self.smooth_weight = smooth_weight
        self.bce_loss = nn.BCEWithLogitsLoss()
        
    def focal_loss(self, predictions, targets):
        """简化的Focal Loss，避免极值"""
        bce_loss = nn.functional.binary_cross_entropy_with_logits(predictions, targets, reduction='none')
        pt = torch.exp(-bce_loss)
        pt = torch.clamp(pt, min=1e-8, max=1-1e-8)
        focal_loss = self.focal_alpha * (1-pt)**self.focal_gamma * bce_loss
        return focal_loss.mean()
    
    def smoothness_loss(self, predictions):
        """简单的平滑损失，促进连续性"""
        pred_prob = torch.sigmoid(predictions)
        diff = torch.abs(pred_prob[:, 1:] - pred_prob[:, :-1])
        return diff.mean()
        
    def forward(self, predictions, targets):
        """稳定的损失计算"""
        bce_loss = self.bce_loss(predictions, targets)
        focal_loss = self.focal_loss(predictions, targets)
        smooth_loss = self.smoothness_loss(predictions)
        
        total_loss = 0.8 * bce_loss + 0.18 * focal_loss + 0.02 * smooth_loss
        
        # 数值稳定性检查
        if torch.isnan(total_loss) or torch.isinf(total_loss):
            print("⚠️ 检测到NaN损失，回退到BCE损失")
            return bce_loss
        
        return total_loss

def create_target_labels(frequency_ranges: List[List[Tuple[float, float]]], 
                        batch_size: int) -> torch.Tensor:
    """从频率范围创建目标标签"""
    targets = torch.zeros(batch_size, Config.FREQ_BINS)
    
    for i, ranges in enumerate(frequency_ranges):
        for start_freq, end_freq in ranges:
            start_bin = mhz_to_freq_bins(np.array([start_freq]), 
                                       Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
            end_bin = mhz_to_freq_bins(np.array([end_freq]), 
                                     Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
            
            start_bin = max(0, min(start_bin, Config.FREQ_BINS - 1))
            end_bin = max(0, min(end_bin, Config.FREQ_BINS - 1))
            
            if start_bin <= end_bin:
                targets[i, start_bin:end_bin+1] = 1.0
    
    return targets

def create_interference_targets(frequency_ranges: List[List[Tuple[float, float]]], 
                               batch_size: int) -> torch.Tensor:
    """从频率范围创建干扰检测目标标签"""
    targets = torch.zeros(batch_size, Config.FREQ_BINS)
    
    for i, ranges in enumerate(frequency_ranges):
        if len(ranges) >= 2:
            # 计算所有信号间的重叠
            for j, (start_freq1, end_freq1) in enumerate(ranges):
                for k, (start_freq2, end_freq2) in enumerate(ranges):
                    if j >= k:
                        continue
                    
                    overlap_start = max(start_freq1, start_freq2)
                    overlap_end = min(end_freq1, end_freq2)
                    
                    if overlap_start < overlap_end:
                        start_bin = mhz_to_freq_bins(np.array([overlap_start]), 
                                                   Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
                        end_bin = mhz_to_freq_bins(np.array([overlap_end]), 
                                                 Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)[0]
                        
                        start_bin = max(0, min(start_bin, Config.FREQ_BINS - 1))
                        end_bin = max(0, min(end_bin, Config.FREQ_BINS - 1))
                        
                        if start_bin <= end_bin:
                            targets[i, start_bin:end_bin+1] = 1.0
    
    return targets

def safe_backward_and_step(loss, model, optimizer, scaler=None, max_grad_norm=0.1):
    """安全的反向传播和参数更新"""
    # 检查损失是否有效
    if torch.isnan(loss) or torch.isinf(loss) or loss.item() > 10.0:
        print(f"⚠️ 跳过异常损失: {loss.item():.4f}")
        optimizer.zero_grad()
        return False
    
    if scaler is not None:
        # 混合精度训练
        scaler.scale(loss).backward()
        scaler.unscale_(optimizer)
        grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)
        
        if torch.isnan(grad_norm) or torch.isinf(grad_norm) or grad_norm > 5.0:
            print(f"⚠️ 跳过异常梯度: {grad_norm:.4f}")
            optimizer.zero_grad()
            scaler.update()
            return False
        
        scaler.step(optimizer)
        scaler.update()
    else:
        # 标准训练
        loss.backward()
        grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)
        
        if torch.isnan(grad_norm) or torch.isinf(grad_norm) or grad_norm > 5.0:
            print(f"⚠️ 跳过异常梯度: {grad_norm:.4f}")
            optimizer.zero_grad()
            return False
        
        optimizer.step()
    
    return True

def process_single_prediction(power_spectrum, task_type, frequency_ranges, model, device, batch_idx):
    """处理单个预测的通用函数，减少重复代码"""
    # 前向传播
    if Config.USE_AMP and device.type == 'cuda':
        with autocast('cuda'):
            predictions, confidence = model(power_spectrum, task_type)
    else:
        predictions, confidence = model(power_spectrum, task_type)
    
    # 创建目标标签
    batch_size = power_spectrum.size(0)
    if task_type == "interference":
        targets = create_interference_targets(frequency_ranges, batch_size).to(device)
    else:
        targets = create_target_labels(frequency_ranges, batch_size).to(device)
    
    return predictions, targets

def train_epoch_stable(model: SpectrumDetector, dataloader, criterion, optimizer, 
                      device: torch.device, epoch: int, scheduler=None, scaler: GradScaler = None) -> Dict[str, float]:
    """数值稳定的训练函数（多任务联合损失版）"""
    model.train()
    total_loss = 0.0
    total_samples = 0
    successful_updates = 0
    
    pbar = tqdm(dataloader, desc=f"训练 Epoch {epoch+1}")
    
    for batch_idx, batch in enumerate(pbar):
        power_spectrum = batch['power_spectrum'].to(device)
        frequency_ranges = batch['labels']
        optimizer.zero_grad()
        try:
            # 同时预测两个任务
            occ_pred, occ_targets = process_single_prediction(
                power_spectrum, "occupancy", frequency_ranges, model, device, batch_idx
            )
            int_pred, int_targets = process_single_prediction(
                power_spectrum, "interference", frequency_ranges, model, device, batch_idx
            )
            occ_loss = criterion(occ_pred, occ_targets)
            int_loss = criterion(int_pred, int_targets)
            # 联合损失，可调权重
            loss = 0.7 * occ_loss + 0.3 * int_loss
            success = safe_backward_and_step(loss, model, optimizer, scaler, max_grad_norm=0.1)
            if success:
                successful_updates += 1
                total_loss += loss.item() * power_spectrum.size(0)
                total_samples += power_spectrum.size(0)
            if scheduler is not None:
                scheduler.step()
        except Exception as e:
            print(f"⚠️ 训练过程中发生异常: {e}")
            optimizer.zero_grad()
            continue
        if total_samples > 0:
            avg_loss = total_loss / total_samples
            success_rate = successful_updates / (batch_idx + 1)
            pbar.set_postfix({
                'loss': f'{loss.item():.4f}' if 'loss' in locals() else 'N/A',
                'avg_loss': f'{avg_loss:.4f}',
                'success': f'{success_rate:.2f}',
            })
    return {
        'loss': total_loss / total_samples if total_samples > 0 else float('inf'),
        'success_rate': successful_updates / len(dataloader) if len(dataloader) > 0 else 0.0
    }

def calculate_interference_intervals(occupancy_intervals: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
    """计算干扰区间的辅助函数"""
    interference_intervals = []
    if len(occupancy_intervals) >= 2:
        sorted_ranges = sorted(occupancy_intervals, key=lambda x: x[0])
        for i, r1 in enumerate(sorted_ranges):
            for j, r2 in enumerate(sorted_ranges):
                if i >= j:
                    continue
                overlap_start = max(r1[0], r2[0])
                overlap_end = min(r1[1], r2[1])
                if overlap_start < overlap_end:
                    interference_intervals.append((overlap_start, overlap_end))
    
    # 合并重叠的干扰区间
    return parse_frequency_ranges(str(interference_intervals))

def convert_bins_to_mhz(segments_bins: List[Tuple[int, int]]) -> List[Tuple[float, float]]:
    """将bin区间转换为MHz区间的辅助函数"""
    if not segments_bins:
        return []
    
    start_bins = np.array([s[0] for s in segments_bins])
    end_bins = np.array([s[1] for s in segments_bins])
    start_freqs = freq_bins_to_mhz(start_bins, Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)
    end_freqs = freq_bins_to_mhz(end_bins, Config.FREQ_MIN, Config.FREQ_MAX, Config.FREQ_BINS)
    
    return list(zip(start_freqs, end_freqs))

def validate_epoch_stable(model: SpectrumDetector, dataloader, criterion, device: torch.device) -> Dict[str, float]:
    """优化的验证函数"""
    model.eval()
    total_loss = 0.0
    total_samples = 0
    
    occupancy_iou_total = 0.0
    interference_iou_total = 0.0
    occupancy_samples = 0
    interference_samples = 0
    
    observation_window_val = (float(Config.FREQ_MIN), float(Config.FREQ_MAX))
    min_segment_width_bins = max(1, int(Config.MIN_SEGMENT_WIDTH * Config.FREQ_BINS / (Config.FREQ_MAX - Config.FREQ_MIN)))

    pbar = tqdm(dataloader, desc="验证中")
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(pbar):
            power_spectrum = batch['power_spectrum'].to(device)
            true_frequency_ranges_batch = batch['labels'] 
            batch_size = power_spectrum.size(0)
            
            try:
                # 分别测试两种任务 - 修复调用方式
                occ_predictions_logits, _ = process_single_prediction(
                    power_spectrum, "occupancy", true_frequency_ranges_batch, model, device, batch_idx
                )
                int_predictions_logits, _ = process_single_prediction(
                    power_spectrum, "interference", true_frequency_ranges_batch, model, device, batch_idx
                )
                
                # 创建目标损失计算用的二进制标签
                occ_targets_for_loss = create_target_labels(true_frequency_ranges_batch, batch_size).to(device)
                int_targets_for_loss = create_interference_targets(true_frequency_ranges_batch, batch_size).to(device)
                
                # 计算损失
                occ_loss = criterion(occ_predictions_logits, occ_targets_for_loss)
                int_loss = criterion(int_predictions_logits, int_targets_for_loss)
                loss = 0.7 * occ_loss + 0.3 * int_loss
                
                if not (torch.isnan(loss) or torch.isinf(loss)):
                    total_loss += loss.item() * batch_size
                    total_samples += batch_size
                
                # IoU 计算
                occ_pred_prob = torch.sigmoid(occ_predictions_logits).cpu().numpy()
                int_pred_prob = torch.sigmoid(int_predictions_logits).cpu().numpy()

                for i in range(batch_size):
                    true_occupancy_intervals = true_frequency_ranges_batch[i]
                    true_interference_intervals = calculate_interference_intervals(true_occupancy_intervals)

                    # 占用检测IoU
                    pred_occupancy_segments_bins = detect_occupied_segments(
                        power_spectrum=occ_pred_prob[i], 
                        threshold=Config.DETECTION_THRESHOLD,
                        min_width=min_segment_width_bins 
                    )
                    pred_occupancy_intervals_mhz = convert_bins_to_mhz(pred_occupancy_segments_bins)
                    
                    occ_iou = calculate_iou(pred_occupancy_intervals_mhz, true_occupancy_intervals, observation_window_val)
                    occupancy_iou_total += occ_iou
                    occupancy_samples += 1
                    
                    # 干扰检测IoU
                    pred_interference_segments_bins = detect_occupied_segments(
                        power_spectrum=int_pred_prob[i],
                        threshold=Config.OVERLAP_THRESHOLD,
                        min_width=min_segment_width_bins
                    )
                    pred_interference_intervals_mhz = convert_bins_to_mhz(pred_interference_segments_bins)

                    # 计算干扰IoU
                    if not true_interference_intervals and not pred_interference_intervals_mhz:
                        int_iou = 1.0
                    elif not true_interference_intervals and pred_interference_intervals_mhz:
                        int_iou = 0.0
                    else:
                        int_iou = calculate_iou(pred_interference_intervals_mhz, true_interference_intervals, observation_window_val)
                    
                    interference_iou_total += int_iou
                    interference_samples += 1
                
            except Exception as e:
                print(f"⚠️ 验证过程中发生异常: {e}")
                import traceback
                traceback.print_exc()
                continue

            # 更新进度条
            if total_samples > 0:
                avg_loss = total_loss / total_samples
                avg_occ_iou = occupancy_iou_total / occupancy_samples if occupancy_samples > 0 else 0
                avg_int_iou = interference_iou_total / interference_samples if interference_samples > 0 else 0
                
                pbar.set_postfix({
                    'loss': f'{avg_loss:.4f}',
                    'occ_iou': f'{avg_occ_iou:.3f}',
                    'int_iou': f'{avg_int_iou:.3f}'
                })
    
    return {
        'loss': total_loss / total_samples if total_samples > 0 else float('inf'),
        'occupancy_iou': occupancy_iou_total / occupancy_samples if occupancy_samples > 0 else 0.0,
        'interference_iou': interference_iou_total / interference_samples if interference_samples > 0 else 0.0,
        'overall_iou': (occupancy_iou_total + interference_iou_total) / (occupancy_samples + interference_samples) if (occupancy_samples + interference_samples) > 0 else 0.0
    }

def check_amp_availability(device: torch.device) -> bool:
    """
    检查自动混合精度训练是否可用
    """
    if device.type != 'cuda':
        return False
    
    # 检查PyTorch版本是否支持AMP
    if not hasattr(torch.amp, 'autocast'):
        return False
    
    # 检查GPU是否支持Tensor Cores (Volta架构及以上)
    if torch.cuda.is_available():
        major, minor = torch.cuda.get_device_capability()
        # Volta (7.0), Turing (7.5), Ampere (8.0+) 支持Tensor Cores
        return major >= 7
    
    return False

def train_model_stable(save_path: str = "model_stable.pth") -> SpectrumDetector:
    """
    数值稳定的训练主函数
    专注于解决梯度爆炸问题
    """
    print("=" * 60)
    print("🛡️ 数值稳定版频谱检测模型训练")
    print("🎯 专注解决梯度爆炸和训练不稳定问题")
    print("=" * 60)
    
    # 设置设备
    device = torch.device(Config.DEVICE)
    print(f"🔧 使用设备: {device}")
    
    if device.type == 'cuda':
        print(f"🎮 GPU型号: {torch.cuda.get_device_name()}")
        print(f"💾 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        # GPU优化
        torch.backends.cudnn.benchmark = True
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
        # 清空显存缓存
        torch.cuda.empty_cache()
        print(f"🧹 显存缓存已清空")
    
    # 加载数据
    print("📊 加载训练数据...")
    train_dataset = load_train_data()
    train_loader, val_loader = create_train_dataloaders(train_dataset)
    
    # 创建模型
    print("🧠 创建模型...")
    model = SpectrumDetector().to(device)
    print(f"⚙️ 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 简化的稳定损失函数
    criterion = StableSpectrumLoss(
        focal_alpha=0.25,
        focal_gamma=1.0,  # 降低gamma，减少梯度放大
        smooth_weight=0.1
    )
    
    print(f"🎯 数值稳定损失函数配置:")
    print(f"   - Focal Alpha: {criterion.focal_alpha}")
    print(f"   - Focal Gamma: {criterion.focal_gamma} (降低以避免梯度爆炸)")
    print(f"   - 平滑权重: {criterion.smooth_weight}")
    
    # 保守的优化器设置
    optimizer = optim.AdamW(
        model.parameters(), 
        lr=Config.LEARNING_RATE * 0.5,  # 降低基础学习率
        weight_decay=1e-4,
        eps=1e-8,  # 增加数值稳定性
        amsgrad=True  # 使用AMSGrad变种，更稳定
    )
    
    # 更保守的学习率调度器
    total_steps = len(train_loader) * Config.NUM_EPOCHS
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=Config.LEARNING_RATE * 1.5,  # 大幅降低峰值学习率 (从4倍降到1.5倍)
        total_steps=total_steps,
        pct_start=0.3,                      # 30%时间用于warm-up
        anneal_strategy='cos',
        div_factor=20,                      # 更温和的起始学习率
        final_div_factor=2000              # 更低的最终学习率
    )
    
    print(f"📈 保守学习率策略:")
    print(f"   - 基础学习率: {Config.LEARNING_RATE * 0.5:.6f}")
    print(f"   - 峰值学习率: {Config.LEARNING_RATE * 1.5:.6f} (大幅降低)")
    print(f"   - 初始学习率: {Config.LEARNING_RATE * 1.5 / 20:.7f}")
    print(f"   - 最终学习率: {Config.LEARNING_RATE * 1.5 / 2000:.8f}")
    print(f"   - 预热比例: 30%")
    
    # 混合精度训练
    scaler = None
    use_amp = Config.USE_AMP and check_amp_availability(device)
    if use_amp:
        scaler = GradScaler(
            init_scale=2**10,  # 降低初始缩放因子，提高稳定性
            growth_factor=1.1,  # 更保守的增长因子
            backoff_factor=0.8,  # 更保守的回退因子
            growth_interval=1000  # 更长的增长间隔
        )
        print(f"✨ 启用保守混合精度训练")
        print(f"🛡️ 配置参数：初始缩放={2**10}, 增长因子=1.1, 回退因子=0.8")
    else:
        print("⚠️ 使用FP32训练")
    
    # 训练历史
    train_history = []
    val_history = []
    best_overall_iou = 0.0
    patience = 20  # 增加耐心值
    patience_counter = 0
    
    print("🎯 开始稳定训练...")
    start_time = time.time()
    
    for epoch in range(Config.NUM_EPOCHS):
        print(f"\n📅 Epoch {epoch+1}/{Config.NUM_EPOCHS}")
        print("-" * 40)
        
        # 训练
        train_metrics = train_epoch_stable(model, train_loader, criterion, optimizer, device, epoch, scheduler, scaler)
        train_history.append(train_metrics)
        
        # 验证
        val_metrics = validate_epoch_stable(model, val_loader, criterion, device)
        val_history.append(val_metrics)
        
        # 打印结果
        print(f"📊 训练损失: {train_metrics['loss']:.4f}")
        print(f"📊 验证损失: {val_metrics['loss']:.4f}")
        print(f"🎯 整体IoU: {val_metrics['overall_iou']:.4f}")
        print(f"🎯 占用检测IoU: {val_metrics['occupancy_iou']:.4f}")
        print(f"🎯 干扰检测IoU: {val_metrics['interference_iou']:.4f}")
        print(f"📈 学习率: {optimizer.param_groups[0]['lr']:.6f}")
        print(f"🎲 干扰训练概率: {train_metrics['success_rate']:.2f}")
        print(f"✅ 成功更新率: {train_metrics['success_rate']:.2f}")
        print(f"⚡ 混合精度: {'启用' if use_amp else '禁用'}")
        
        # 显示显存使用情况
        if device.type == 'cuda':
            memory_used = torch.cuda.memory_allocated() / 1e9
            memory_cached = torch.cuda.memory_reserved() / 1e9
            print(f"💾 显存使用: {memory_used:.1f}GB (缓存: {memory_cached:.1f}GB)")
        
        # 保存最佳模型
        current_iou = val_metrics['overall_iou']
        if current_iou > best_overall_iou:
            best_overall_iou = current_iou
            patience_counter = 0
            
            checkpoint_data = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'train_history': train_history,
                'val_history': val_history,
                'best_overall_iou': best_overall_iou,
                'use_amp': use_amp,
                'config': Config
            }
            
            if scaler is not None:
                checkpoint_data['scaler_state_dict'] = scaler.state_dict()
            
            torch.save(checkpoint_data, save_path)
            print(f"💾 保存最佳模型，整体IoU: {best_overall_iou:.4f}")
        else:
            patience_counter += 1
            
        # 检查训练稳定性
        if train_metrics['success_rate'] < 0.8:
            print(f"⚠️ 警告：成功更新率过低 ({train_metrics['success_rate']:.2f})")
            print("🔧 建议降低学习率或检查数据质量")
        
        # 早停
        if patience_counter >= patience:
            print(f"⏰ 早停：{patience} epochs无改善")
            break
    
    # 训练完成
    total_time = time.time() - start_time
    print(f"\n🎉 稳定训练完成！")
    print(f"⏱️ 总用时: {total_time:.2f}秒")
    print(f"🏆 最佳整体IoU: {best_overall_iou:.4f}")
    
    # 加载最佳模型
    if os.path.exists(save_path):
        checkpoint = torch.load(save_path)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"✅ 已加载最佳稳定模型")
    
    return model

if __name__ == "__main__":
    # 设置随机种子以便复现
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 开始稳定训练
    trained_model = train_model_stable()
    print("🎊 稳定训练完成！模型已保存为 model_stable.pth") 