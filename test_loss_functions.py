#!/usr/bin/env python3
"""
损失函数修复验证脚本
测试新的损失函数设计的正确性和稳定性
"""

import torch
import torch.nn.functional as F
import numpy as np
from config import Config
from train import SpectrumDetectionLoss, MultiTaskLossManager

def test_spectrum_detection_loss():
    """测试频谱检测损失函数"""
    print("=" * 60)
    print("🧪 测试频谱检测损失函数")
    print("=" * 60)
    
    device = torch.device(Config.DEVICE)
    loss_fn = SpectrumDetectionLoss(
        focal_alpha=0.25,
        focal_gamma=2.0,
        dice_weight=0.3,
        boundary_weight=0.1
    ).to(device)
    
    # 创建测试数据
    batch_size = 8
    freq_bins = Config.FREQ_BINS

    # 测试场景1: 平衡数据
    print("📊 测试场景1: 平衡数据")
    predictions = torch.randn(batch_size, freq_bins, requires_grad=True).to(device)
    targets = torch.randint(0, 2, (batch_size, freq_bins)).float().to(device)

    loss = loss_fn(predictions, targets)
    print(f"   损失值: {loss.item():.4f}")

    # 检查梯度
    loss.backward()
    print(f"   梯度计算: ✅")
    
    # 测试场景2: 不平衡数据 (稀疏信号)
    print("\n📊 测试场景2: 不平衡数据 (10%正样本)")
    predictions = torch.randn(batch_size, freq_bins).to(device)
    targets = torch.zeros(batch_size, freq_bins).to(device)
    # 只有10%的位置为正样本
    positive_mask = torch.rand(batch_size, freq_bins) < 0.1
    targets[positive_mask] = 1.0
    
    loss = loss_fn(predictions, targets)
    print(f"   损失值: {loss.item():.4f}")
    print(f"   正样本比例: {targets.mean().item():.3f}")
    
    # 测试场景3: 边界检测
    print("\n📊 测试场景3: 边界检测")
    predictions = torch.randn(batch_size, freq_bins).to(device)
    targets = torch.zeros(batch_size, freq_bins).to(device)
    # 创建明确的边界
    for i in range(batch_size):
        start = np.random.randint(100, freq_bins-200)
        end = start + np.random.randint(50, 150)
        targets[i, start:end] = 1.0
    
    loss = loss_fn(predictions, targets)
    print(f"   损失值: {loss.item():.4f}")
    print(f"   边界数量: {torch.sum(torch.abs(targets[:, 1:] - targets[:, :-1])).item()}")
    
    # 测试数值稳定性
    print("\n📊 测试场景4: 数值稳定性")
    extreme_predictions = torch.tensor([[-100.0, 100.0] * (freq_bins//2)]).repeat(batch_size, 1).to(device)
    extreme_targets = torch.tensor([[0.0, 1.0] * (freq_bins//2)]).repeat(batch_size, 1).to(device)
    
    loss = loss_fn(extreme_predictions, extreme_targets)
    print(f"   极值损失: {loss.item():.4f}")
    
    if torch.isnan(loss) or torch.isinf(loss):
        print("   ❌ 数值不稳定")
        return False
    else:
        print("   ✅ 数值稳定")
        return True

def test_multitask_loss_manager():
    """测试多任务损失管理器"""
    print("\n" + "=" * 60)
    print("🎯 测试多任务损失管理器")
    print("=" * 60)
    
    device = torch.device(Config.DEVICE)
    loss_manager = MultiTaskLossManager(
        task_weights={'occupancy': 1.0, 'interference': 0.8, 'overlap': 0.6},
        adaptive_weights=True
    ).to(device)
    
    batch_size = 4
    freq_bins = Config.FREQ_BINS
    
    # 准备测试数据
    predictions_dict = {}
    targets_dict = {}
    
    # 占用检测任务
    predictions_dict['occupancy'] = torch.randn(batch_size, freq_bins).to(device)
    targets_dict['occupancy'] = torch.randint(0, 2, (batch_size, freq_bins)).float().to(device)
    
    # 干扰检测任务
    predictions_dict['interference'] = torch.randn(batch_size, freq_bins).to(device)
    targets_dict['interference'] = torch.randint(0, 2, (batch_size, freq_bins)).float().to(device)
    
    # 重叠检测任务 (4类)
    predictions_dict['overlap'] = torch.randn(batch_size, freq_bins, 4).to(device)
    targets_dict['overlap'] = torch.randint(0, 4, (batch_size, freq_bins)).to(device)
    
    print("📊 多任务数据:")
    print(f"   占用检测: {predictions_dict['occupancy'].shape}")
    print(f"   干扰检测: {predictions_dict['interference'].shape}")
    print(f"   重叠检测: {predictions_dict['overlap'].shape}")
    
    # 计算多任务损失
    total_loss, individual_losses = loss_manager(predictions_dict, targets_dict)
    
    print(f"\n📊 损失计算结果:")
    print(f"   总损失: {total_loss.item():.4f}")
    for task, loss_val in individual_losses.items():
        print(f"   {task}损失: {loss_val.item():.4f}")
    
    # 测试自适应权重
    print(f"\n📊 自适应权重:")
    if loss_manager.adaptive_weights:
        weights = torch.exp(-loss_manager.weight_params)
        for i, task in enumerate(loss_manager.task_names):
            print(f"   {task}: {weights[i].item():.3f}")
    
    # 测试梯度
    total_loss.backward()
    print(f"\n✅ 梯度计算成功")
    
    # 检查数值稳定性
    if torch.isnan(total_loss) or torch.isinf(total_loss):
        print("❌ 多任务损失数值不稳定")
        return False
    else:
        print("✅ 多任务损失数值稳定")
        return True

def test_loss_components():
    """测试损失函数各组件"""
    print("\n" + "=" * 60)
    print("🔧 测试损失函数组件")
    print("=" * 60)
    
    device = torch.device(Config.DEVICE)
    loss_fn = SpectrumDetectionLoss().to(device)
    
    batch_size = 4
    freq_bins = 512  # 使用较小的尺寸加快测试
    
    predictions = torch.randn(batch_size, freq_bins).to(device)
    targets = torch.randint(0, 2, (batch_size, freq_bins)).float().to(device)
    
    # 测试各个组件
    print("📊 测试Focal Loss:")
    focal_loss = loss_fn.focal_loss(predictions, targets)
    print(f"   Focal Loss: {focal_loss.item():.4f}")
    
    print("\n📊 测试Dice Loss:")
    dice_loss = loss_fn.dice_loss(predictions, targets)
    print(f"   Dice Loss: {dice_loss.item():.4f}")
    
    print("\n📊 测试边界感知损失:")
    boundary_loss = loss_fn.boundary_aware_loss(predictions, targets)
    print(f"   Boundary Loss: {boundary_loss.item():.4f}")
    
    # 测试组合损失
    print("\n📊 测试组合损失:")
    total_loss = loss_fn(predictions, targets)
    print(f"   Total Loss: {total_loss.item():.4f}")
    
    # 验证损失组件的合理性
    expected_total = focal_loss + loss_fn.dice_weight * dice_loss + loss_fn.boundary_weight * boundary_loss
    print(f"   期望总损失: {expected_total.item():.4f}")
    print(f"   差异: {abs(total_loss.item() - expected_total.item()):.6f}")
    
    if abs(total_loss.item() - expected_total.item()) < 1e-5:
        print("   ✅ 损失组合正确")
        return True
    else:
        print("   ❌ 损失组合有误")
        return False

def test_gradient_flow():
    """测试梯度流动"""
    print("\n" + "=" * 60)
    print("🌊 测试梯度流动")
    print("=" * 60)
    
    device = torch.device(Config.DEVICE)
    
    # 创建简单的测试模型
    class SimpleModel(torch.nn.Module):
        def __init__(self):
            super().__init__()
            self.conv = torch.nn.Conv1d(1, 64, 3, padding=1)
            self.fc = torch.nn.Conv1d(64, 1, 1)
        
        def forward(self, x):
            x = x.unsqueeze(1)  # 添加通道维度
            x = torch.relu(self.conv(x))
            x = self.fc(x)
            return x.squeeze(1)
    
    model = SimpleModel().to(device)
    loss_fn = SpectrumDetectionLoss().to(device)
    
    # 测试数据
    batch_size = 4
    freq_bins = 256
    
    inputs = torch.randn(batch_size, freq_bins).to(device)
    targets = torch.randint(0, 2, (batch_size, freq_bins)).float().to(device)
    
    # 前向传播
    outputs = model(inputs)
    loss = loss_fn(outputs, targets)
    
    print(f"📊 前向传播:")
    print(f"   输入形状: {inputs.shape}")
    print(f"   输出形状: {outputs.shape}")
    print(f"   损失值: {loss.item():.4f}")
    
    # 反向传播
    loss.backward()
    
    # 检查梯度
    total_grad_norm = 0.0
    param_count = 0
    
    for name, param in model.named_parameters():
        if param.grad is not None:
            grad_norm = param.grad.norm().item()
            total_grad_norm += grad_norm
            param_count += 1
            print(f"   {name}: 梯度范数 = {grad_norm:.4f}")
        else:
            print(f"   {name}: 无梯度")
    
    avg_grad_norm = total_grad_norm / param_count if param_count > 0 else 0
    print(f"\n📊 梯度统计:")
    print(f"   平均梯度范数: {avg_grad_norm:.4f}")
    print(f"   有梯度的参数: {param_count}")
    
    if avg_grad_norm > 0 and avg_grad_norm < 100:
        print("   ✅ 梯度流动正常")
        return True
    else:
        print("   ❌ 梯度流动异常")
        return False

def main():
    """主测试函数"""
    print("🚀 开始损失函数修复验证")
    
    test_results = []
    
    try:
        # 测试1: 频谱检测损失函数
        result1 = test_spectrum_detection_loss()
        test_results.append(("频谱检测损失", result1))
        
        # 测试2: 多任务损失管理器
        result2 = test_multitask_loss_manager()
        test_results.append(("多任务损失管理", result2))
        
        # 测试3: 损失函数组件
        result3 = test_loss_components()
        test_results.append(("损失函数组件", result3))
        
        # 测试4: 梯度流动
        result4 = test_gradient_flow()
        test_results.append(("梯度流动", result4))
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎉 损失函数修复验证完成")
    print("=" * 60)
    
    print("\n测试结果:")
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"- {test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n总体结果: {'✅ 全部通过' if all_passed else '❌ 部分失败'}")
    
    print("\n主要修复内容:")
    print("1. ✅ 修复了Focal Loss的数学实现错误")
    print("2. ✅ 添加了Dice Loss处理类别不平衡")
    print("3. ✅ 实现了边界感知损失提升精度")
    print("4. ✅ 设计了多任务损失管理器解决冲突")
    print("5. ✅ 放宽了梯度裁剪限制，提升训练稳定性")
    
    print("\n建议下一步:")
    if all_passed:
        print("- 损失函数修复完成，可以开始训练")
        print("- 建议先进行小规模训练验证效果")
    else:
        print("- 需要进一步调试失败的测试项")
    
    return all_passed

if __name__ == "__main__":
    main()
