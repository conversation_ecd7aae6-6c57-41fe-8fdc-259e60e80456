import os
import torch

class Config:
    """配置类，包含所有超参数和路径设置"""
    
    # 数据路径
    TRAIN_DATA_PATH = "data_and_code/train.h5"
    TEST_DATA_PATH = "data_and_code/test_public.h5"
    OUTPUT_PATH = "predictions.txt"
    MODEL_SAVE_PATH = "model_stable.pth"
    
    # 频谱参数
    FREQ_MIN = 2400  # MHz
    FREQ_MAX = 2500  # MHz
    SAMPLE_RATE = 100e6  # 100 MS/s
    FREQ_BINS = 4096  # 提升FFT分辨率，更精细的频率分析
    INPUT_LENGTH = 100000  # 采样点数
    FREQ_RESOLUTION = (FREQ_MAX - FREQ_MIN) / FREQ_BINS  # 约0.049 MHz/bin
    
    # 训练参数 - 4090 GPU优化
    BATCH_SIZE = 32
    LEARNING_RATE = 1e-4
    NUM_EPOCHS = 60
    TRAIN_SPLIT = 0.85
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
    
    # 混合精度训练
    USE_AMP = True
    AMP_OPT_LEVEL = "O1"
    
    # 模型架构参数
    EMBED_DIM = 512
    NUM_ATTENTION_HEADS = 8
    TRANSFORMER_LAYERS = 6
    FF_DIM = 2048
    DROPOUT_RATE = 0.15
    CNN_CHANNELS = [64, 128, 256, 512]
    
    # 检测参数
    DETECTION_THRESHOLD = 0.25
    MIN_SEGMENT_WIDTH = 1.0
    OVERLAP_THRESHOLD = 0.5
    SMOOTHING_WINDOW = 5
    MULTI_SCALE_KERNELS = [3, 5, 7, 11, 15]
    DILATED_RATES = [1, 2, 4, 8]
    
    # 高级检测策略
    USE_ENSEMBLE = True
    NMS_THRESHOLD = 0.35
    CONFIDENCE_THRESHOLD = 0.55
    
    # 数据增强参数 - 合并相关配置
    AUGMENTATION_CONFIG = {
        'prob': 0.8,                        # 总概率
        'noise_prob': 0.7,                  # 噪声概率
        'amplitude_prob': 0.5,              # 幅度缩放概率
        'phase_prob': 0.4,                  # 相位旋转概率
        'freq_shift_prob': 0.3,             # 频率偏移概率
        'time_shift_prob': 0.2,             # 时域偏移概率
        'interference_prob': 0.3,           # 干扰信号概率
        'multipath_prob': 0.2,              # 多径衰落概率
        
        # 参数范围
        'snr_range': (10, 30),              # 信噪比范围 (dB)
        'amplitude_scale_range': (0.7, 1.3), # 幅度缩放范围
        'phase_rotation_max': 45.0,         # 最大相位旋转 (度)
        'freq_shift_max': 5.0,              # 最大频率偏移 (MHz)
        'time_shift_max': 1000,             # 最大时域偏移 (样本)
        'interference_count_range': (2, 5), # 干扰信号数量范围
        'multipath_count': 3,               # 多径数量
        'gaussian_sigma': 2.0               # 高斯平滑参数
    }
    
    # 向后兼容的单独属性访问
    @property
    def AUGMENTATION_PROB(self): return self.AUGMENTATION_CONFIG['prob']
    @property
    def NOISE_PROB(self): return self.AUGMENTATION_CONFIG['noise_prob']
    @property
    def AMPLITUDE_PROB(self): return self.AUGMENTATION_CONFIG['amplitude_prob']
    @property
    def PHASE_PROB(self): return self.AUGMENTATION_CONFIG['phase_prob']
    @property
    def FREQ_SHIFT_PROB(self): return self.AUGMENTATION_CONFIG['freq_shift_prob']
    @property
    def TIME_SHIFT_PROB(self): return self.AUGMENTATION_CONFIG['time_shift_prob']
    @property
    def INTERFERENCE_PROB(self): return self.AUGMENTATION_CONFIG['interference_prob']
    @property
    def MULTIPATH_PROB(self): return self.AUGMENTATION_CONFIG['multipath_prob']
    @property
    def SNR_RANGE(self): return self.AUGMENTATION_CONFIG['snr_range']
    @property
    def AMPLITUDE_SCALE_RANGE(self): return self.AUGMENTATION_CONFIG['amplitude_scale_range']
    @property
    def PHASE_ROTATION_MAX(self): return self.AUGMENTATION_CONFIG['phase_rotation_max']
    @property
    def FREQ_SHIFT_MAX(self): return self.AUGMENTATION_CONFIG['freq_shift_max']
    @property
    def TIME_SHIFT_MAX(self): return self.AUGMENTATION_CONFIG['time_shift_max']
    @property
    def INTERFERENCE_COUNT_RANGE(self): return self.AUGMENTATION_CONFIG['interference_count_range']
    @property
    def MULTIPATH_COUNT(self): return self.AUGMENTATION_CONFIG['multipath_count']
    @property
    def GAUSSIAN_SIGMA(self): return self.AUGMENTATION_CONFIG['gaussian_sigma'] 